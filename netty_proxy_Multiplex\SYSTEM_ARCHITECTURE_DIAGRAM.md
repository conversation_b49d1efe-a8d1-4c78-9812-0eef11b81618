# 多路复用代理系统架构图

本文档包含完整的系统架构图，展示客户端和服务器端的详细组件结构和交互关系。

## 🏗️ 整体系统架构

```mermaid
graph TB
    subgraph "用户应用层"
        Browser[浏览器]
        App[应用程序]
        Tool[开发工具]
    end

    subgraph "Proxy Client 代理客户端"
        subgraph "接入层"
            SOCKS5[SOCKS5代理服务<br/>端口:1080]
            HTTP[HTTP代理服务<br/>端口:8080]
        end
        
        subgraph "协议处理层"
            S5Handler[MultiplexSocks5Handler<br/>SOCKS5协议处理]
            HttpHandler[HttpProxyHandler<br/>HTTP协议处理]
        end
        
        subgraph "智能分流层"
            AddressFilter[AddressFilter<br/>地址过滤器]
            GeoIPClient[GeoIPUtil<br/>IP地理位置判断]
        end
        
        subgraph "连接管理层"
            ConnMgr[ConnectionManager<br/>多路复用连接管理]
            DirectConn[DirectConnectionHandler<br/>直连处理器]
            SessionMgr[会话管理器]
        end
        
        subgraph "配置管理"
            ClientConfig[ProxyClientConfig<br/>客户端配置]
            ClientSSL[ClientSslContextManager<br/>SSL上下文管理]
        end
    end

    subgraph "网络传输层"
        Internet[互联网]
        Encrypted[加密通道<br/>SSL/TLS]
    end

    subgraph "Proxy Server 代理服务器"
        subgraph "网络接入层"
            ServerBootstrap[ServerBootstrap<br/>Netty服务器启动]
            ProxyServerMain[ProxyServer<br/>主服务器:8888]
        end
        
        subgraph "连接处理层"
            Initializer[ProxyServerInitializer<br/>连接初始化器]
            ProtocolDetector[MultiplexProtocolDetector<br/>协议检测器]
            ProxyHandler[MultiplexProxyHandler<br/>多路复用处理器]
        end
        
        subgraph "认证与安全层"
            AuthMgr[AuthManager<br/>认证管理器]
            AuthConfig[AuthConfig<br/>认证配置]
            ServerSSL[SslContextManager<br/>SSL上下文管理]
        end
        
        subgraph "过滤与安全层"
            GeoFilter[GeoLocationFilter<br/>地理位置过滤器]
            GeoIPServer[GeoIPUtil<br/>IP地理位置判断]
            MaliciousLoader[MaliciousContentLoader<br/>恶意内容加载器]
            Blacklist[HostBlacklist<br/>主机黑名单]
        end
        
        subgraph "连接池与后端"
            ConnPool[ConnectionPool<br/>连接池管理]
            BackendHandler[MultiplexBackendHandler<br/>后端连接处理器]
            PoolConfig[ConnectionPoolConfig<br/>连接池配置]
        end
        
        subgraph "监控与配置"
            PerfMetrics[PerformanceMetrics<br/>性能监控]
            ConfigMgr[ProxyServerConfigManager<br/>配置管理器]
            Properties[ProxyServerProperties<br/>配置属性]
        end
    end

    subgraph "目标服务器"
        WebServer[Web服务器]
        APIServer[API服务器]
        Database[数据库服务器]
    end

    %% 用户到客户端的连接
    Browser --> SOCKS5
    App --> HTTP
    Tool --> SOCKS5

    %% 客户端内部流程
    SOCKS5 --> S5Handler
    HTTP --> HttpHandler
    S5Handler --> AddressFilter
    HttpHandler --> AddressFilter
    AddressFilter --> GeoIPClient
    AddressFilter --> ConnMgr
    AddressFilter --> DirectConn
    ConnMgr --> SessionMgr
    ClientConfig --> AddressFilter
    ClientSSL --> ConnMgr

    %% 客户端到服务器
    ConnMgr --> Encrypted
    Encrypted --> ServerBootstrap

    %% 直连路径
    DirectConn --> Internet

    %% 服务器内部流程
    ServerBootstrap --> Initializer
    Initializer --> ProtocolDetector
    ProtocolDetector --> ProxyHandler
    ProxyHandler --> AuthMgr
    AuthMgr --> AuthConfig
    ProxyHandler --> GeoFilter
    GeoFilter --> GeoIPServer
    GeoFilter --> MaliciousLoader
    ProxyHandler --> Blacklist
    ProxyHandler --> ConnPool
    ConnPool --> BackendHandler
    ConnPool --> PoolConfig
    ProxyHandler --> PerfMetrics
    ConfigMgr --> Properties
    ServerSSL --> ProxyHandler

    %% 服务器到目标服务器
    BackendHandler --> Internet
    Internet --> WebServer
    Internet --> APIServer
    Internet --> Database

    %% 样式定义
    classDef clientBox fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef serverBox fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef networkBox fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef configBox fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef securityBox fill:#ffebee,stroke:#b71c1c,stroke-width:2px

    class SOCKS5,HTTP,S5Handler,HttpHandler,AddressFilter,ConnMgr,DirectConn clientBox
    class ProxyServerMain,Initializer,ProtocolDetector,ProxyHandler,ConnPool,BackendHandler serverBox
    class Internet,Encrypted,WebServer,APIServer,Database networkBox
    class ClientConfig,ConfigMgr,Properties,PoolConfig configBox
    class AuthMgr,GeoFilter,Blacklist,ServerSSL,ClientSSL securityBox
```

## 🔄 数据流架构图

```mermaid
sequenceDiagram
    participant User as 用户应用
    participant Client as Proxy Client
    participant Filter as 地址过滤器
    participant ConnMgr as 连接管理器
    participant Server as Proxy Server
    participant Auth as 认证管理
    participant GeoFilter as 地理过滤
    participant Pool as 连接池
    participant Target as 目标服务器

    User->>Client: 1. 发起代理请求
    Client->>Filter: 2. 地址过滤判断
    
    alt 中国IP直连
        Filter->>Target: 3a. 直接连接
        Target-->>User: 4a. 返回响应
    else 海外IP代理
        Filter->>ConnMgr: 3b. 使用代理连接
        ConnMgr->>Server: 4b. 建立多路复用连接
        Server->>Auth: 5. 身份认证
        Auth-->>Server: 6. 认证结果
        Server->>GeoFilter: 7. 地理位置过滤
        GeoFilter-->>Server: 8. 过滤结果
        Server->>Pool: 9. 获取后端连接
        Pool->>Target: 10. 连接目标服务器
        Target-->>Pool: 11. 响应数据
        Pool-->>Server: 12. 转发响应
        Server-->>ConnMgr: 13. 多路复用响应
        ConnMgr-->>Client: 14. 返回给客户端
        Client-->>User: 15. 最终响应
    end
```

## 🏛️ 组件层次架构

```mermaid
graph TD
    subgraph "应用层 Application Layer"
        A1[用户应用程序]
        A2[浏览器]
        A3[开发工具]
    end

    subgraph "代理接入层 Proxy Access Layer"
        B1[SOCKS5代理服务]
        B2[HTTP代理服务]
    end

    subgraph "协议处理层 Protocol Processing Layer"
        C1[SOCKS5协议处理器]
        C2[HTTP协议处理器]
        C3[协议检测器]
    end

    subgraph "智能路由层 Intelligent Routing Layer"
        D1[地址过滤器]
        D2[GeoIP判断]
        D3[路由决策]
    end

    subgraph "连接管理层 Connection Management Layer"
        E1[多路复用连接管理]
        E2[会话管理]
        E3[直连处理]
    end

    subgraph "安全认证层 Security Authentication Layer"
        F1[身份认证]
        F2[SSL/TLS加密]
        F3[地理位置过滤]
        F4[恶意内容过滤]
    end

    subgraph "连接池层 Connection Pool Layer"
        G1[连接池管理]
        G2[连接复用]
        G3[健康检查]
    end

    subgraph "后端处理层 Backend Processing Layer"
        H1[后端连接处理]
        H2[数据转发]
        H3[负载均衡]
    end

    subgraph "监控配置层 Monitoring Configuration Layer"
        I1[性能监控]
        I2[配置管理]
        I3[日志记录]
    end

    subgraph "网络传输层 Network Transport Layer"
        J1[TCP/IP协议栈]
        J2[SSL/TLS协议]
        J3[网络接口]
    end

    A1 --> B1
    A2 --> B2
    A3 --> B1
    B1 --> C1
    B2 --> C2
    C1 --> D1
    C2 --> D1
    C3 --> D1
    D1 --> D2
    D2 --> D3
    D3 --> E1
    D3 --> E3
    E1 --> E2
    E1 --> F1
    F1 --> F2
    F2 --> F3
    F3 --> F4
    F4 --> G1
    G1 --> G2
    G2 --> G3
    G3 --> H1
    H1 --> H2
    H2 --> H3
    E1 --> I1
    F1 --> I2
    G1 --> I3
    H1 --> J1
    F2 --> J2
    E3 --> J3

    classDef appLayer fill:#e3f2fd,stroke:#1976d2
    classDef proxyLayer fill:#e8f5e8,stroke:#388e3c
    classDef protocolLayer fill:#fff3e0,stroke:#f57c00
    classDef routingLayer fill:#fce4ec,stroke:#c2185b
    classDef connectionLayer fill:#f3e5f5,stroke:#7b1fa2
    classDef securityLayer fill:#ffebee,stroke:#d32f2f
    classDef poolLayer fill:#e0f2f1,stroke:#00796b
    classDef backendLayer fill:#f1f8e9,stroke:#689f38
    classDef monitorLayer fill:#fff8e1,stroke:#fbc02d
    classDef networkLayer fill:#e8eaf6,stroke:#3f51b5

    class A1,A2,A3 appLayer
    class B1,B2 proxyLayer
    class C1,C2,C3 protocolLayer
    class D1,D2,D3 routingLayer
    class E1,E2,E3 connectionLayer
    class F1,F2,F3,F4 securityLayer
    class G1,G2,G3 poolLayer
    class H1,H2,H3 backendLayer
    class I1,I2,I3 monitorLayer
    class J1,J2,J3 networkLayer
```

## 🔧 技术栈架构图

```mermaid
graph TB
    subgraph "开发技术栈"
        subgraph "编程语言"
            Java17[Java 17<br/>主要开发语言]
        end

        subgraph "网络框架"
            Netty[Netty 4.1.100<br/>高性能网络框架]
            NIO[Java NIO<br/>非阻塞IO]
        end

        subgraph "构建工具"
            Maven[Maven 3.x<br/>项目构建管理]
            Shade[Maven Shade Plugin<br/>Fat JAR打包]
        end

        subgraph "配置管理"
            YAML[SnakeYAML<br/>配置文件解析]
            Properties[Properties<br/>配置属性管理]
        end

        subgraph "日志系统"
            SLF4J[SLF4J<br/>日志门面]
            Logback[Logback<br/>日志实现]
        end

        subgraph "测试框架"
            JUnit5[JUnit 5<br/>单元测试框架]
            Mockito[Mockito<br/>模拟测试]
        end

        subgraph "安全组件"
            SSL[Java SSL/TLS<br/>加密通信]
            Crypto[Java Crypto<br/>加密算法]
        end

        subgraph "数据处理"
            Collections[Java Collections<br/>数据结构]
            Concurrent[Java Concurrent<br/>并发工具]
        end
    end

    Java17 --> Netty
    Netty --> NIO
    Maven --> Shade
    YAML --> Properties
    SLF4J --> Logback
    JUnit5 --> Mockito
    SSL --> Crypto
    Collections --> Concurrent

    classDef langBox fill:#ff9800,color:#fff
    classDef frameworkBox fill:#4caf50,color:#fff
    classDef buildBox fill:#2196f3,color:#fff
    classDef configBox fill:#9c27b0,color:#fff
    classDef logBox fill:#607d8b,color:#fff
    classDef testBox fill:#795548,color:#fff
    classDef securityBox fill:#f44336,color:#fff
    classDef dataBox fill:#009688,color:#fff

    class Java17 langBox
    class Netty,NIO frameworkBox
    class Maven,Shade buildBox
    class YAML,Properties configBox
    class SLF4J,Logback logBox
    class JUnit5,Mockito testBox
    class SSL,Crypto securityBox
    class Collections,Concurrent dataBox
```

## 🚀 部署架构图

```mermaid
graph TB
    subgraph "生产环境部署"
        subgraph "负载均衡层"
            LB[负载均衡器<br/>Nginx/HAProxy]
        end

        subgraph "代理服务器集群"
            PS1[Proxy Server 1<br/>主节点]
            PS2[Proxy Server 2<br/>备用节点]
            PS3[Proxy Server 3<br/>扩展节点]
        end

        subgraph "客户端部署"
            PC1[Proxy Client 1<br/>办公网络]
            PC2[Proxy Client 2<br/>家庭网络]
            PC3[Proxy Client 3<br/>移动网络]
        end

        subgraph "监控系统"
            Monitor[监控服务<br/>Prometheus/Grafana]
            Alert[告警系统<br/>AlertManager]
        end

        subgraph "配置管理"
            Config[配置中心<br/>Consul/Etcd]
            Registry[服务注册<br/>服务发现]
        end

        subgraph "日志系统"
            LogCollector[日志收集<br/>Filebeat/Fluentd]
            LogStorage[日志存储<br/>Elasticsearch]
            LogAnalysis[日志分析<br/>Kibana]
        end

        subgraph "数据存储"
            Redis[Redis缓存<br/>会话存储]
            DB[数据库<br/>MySQL/PostgreSQL]
        end
    end

    PC1 --> LB
    PC2 --> LB
    PC3 --> LB
    LB --> PS1
    LB --> PS2
    LB --> PS3
    PS1 --> Monitor
    PS2 --> Monitor
    PS3 --> Monitor
    Monitor --> Alert
    PS1 --> Config
    PS2 --> Config
    PS3 --> Config
    Config --> Registry
    PS1 --> LogCollector
    PS2 --> LogCollector
    PS3 --> LogCollector
    LogCollector --> LogStorage
    LogStorage --> LogAnalysis
    PS1 --> Redis
    PS2 --> Redis
    PS3 --> Redis
    PS1 --> DB
    PS2 --> DB
    PS3 --> DB

    classDef lbBox fill:#ff5722,color:#fff
    classDef serverBox fill:#3f51b5,color:#fff
    classDef clientBox fill:#4caf50,color:#fff
    classDef monitorBox fill:#ff9800,color:#fff
    classDef configBox fill:#9c27b0,color:#fff
    classDef logBox fill:#607d8b,color:#fff
    classDef dataBox fill:#009688,color:#fff

    class LB lbBox
    class PS1,PS2,PS3 serverBox
    class PC1,PC2,PC3 clientBox
    class Monitor,Alert monitorBox
    class Config,Registry configBox
    class LogCollector,LogStorage,LogAnalysis logBox
    class Redis,DB dataBox
```

## 📊 性能监控架构图

```mermaid
graph LR
    subgraph "数据采集层"
        A1[连接数统计]
        A2[会话数统计]
        A3[流量统计]
        A4[延迟统计]
        A5[错误率统计]
    end

    subgraph "数据处理层"
        B1[数据聚合]
        B2[数据清洗]
        B3[指标计算]
        B4[异常检测]
    end

    subgraph "存储层"
        C1[时序数据库<br/>InfluxDB]
        C2[缓存层<br/>Redis]
        C3[历史数据<br/>MySQL]
    end

    subgraph "展示层"
        D1[实时监控<br/>Grafana]
        D2[告警通知<br/>AlertManager]
        D3[报表系统<br/>自定义报表]
    end

    A1 --> B1
    A2 --> B1
    A3 --> B2
    A4 --> B3
    A5 --> B4
    B1 --> C1
    B2 --> C2
    B3 --> C3
    B4 --> C1
    C1 --> D1
    C2 --> D1
    C3 --> D3
    D1 --> D2

    classDef collectBox fill:#4caf50,color:#fff
    classDef processBox fill:#ff9800,color:#fff
    classDef storageBox fill:#2196f3,color:#fff
    classDef displayBox fill:#9c27b0,color:#fff

    class A1,A2,A3,A4,A5 collectBox
    class B1,B2,B3,B4 processBox
    class C1,C2,C3 storageBox
    class D1,D2,D3 displayBox
```

## 🔒 安全架构图

```mermaid
graph TB
    subgraph "安全防护体系"
        subgraph "网络层安全"
            N1[DDoS防护]
            N2[IP白名单/黑名单]
            N3[地理位置过滤]
            N4[连接频率限制]
        end

        subgraph "传输层安全"
            T1[SSL/TLS加密]
            T2[证书管理]
            T3[密钥轮换]
            T4[加密算法选择]
        end

        subgraph "应用层安全"
            A1[身份认证]
            A2[访问控制]
            A3[会话管理]
            A4[权限验证]
        end

        subgraph "内容安全"
            C1[恶意域名过滤]
            C2[关键词检测]
            C3[白名单保护]
            C4[内容审计]
        end

        subgraph "监控安全"
            M1[安全日志]
            M2[异常检测]
            M3[入侵检测]
            M4[安全告警]
        end
    end

    N1 --> T1
    N2 --> T2
    N3 --> A1
    N4 --> A2
    T1 --> C1
    T2 --> C2
    A1 --> M1
    A2 --> M2
    C1 --> M3
    C2 --> M4

    classDef networkSec fill:#f44336,color:#fff
    classDef transportSec fill:#ff9800,color:#fff
    classDef appSec fill:#4caf50,color:#fff
    classDef contentSec fill:#2196f3,color:#fff
    classDef monitorSec fill:#9c27b0,color:#fff

    class N1,N2,N3,N4 networkSec
    class T1,T2,T3,T4 transportSec
    class A1,A2,A3,A4 appSec
    class C1,C2,C3,C4 contentSec
    class M1,M2,M3,M4 monitorSec
```

## ⚙️ 配置管理架构图

```mermaid
graph TD
    subgraph "配置管理体系"
        subgraph "配置源"
            S1[YAML配置文件]
            S2[环境变量]
            S3[命令行参数]
            S4[外部配置中心]
        end

        subgraph "配置加载器"
            L1[ProxyServerConfigManager]
            L2[ProxyClientConfigManager]
            L3[配置文件解析器]
            L4[配置验证器]
        end

        subgraph "配置存储"
            ST1[内存配置缓存]
            ST2[配置快照]
            ST3[配置历史版本]
            ST4[默认配置]
        end

        subgraph "配置应用"
            A1[服务器配置]
            A2[客户端配置]
            A3[安全配置]
            A4[性能配置]
        end

        subgraph "配置监控"
            M1[配置变更检测]
            M2[配置热重载]
            M3[配置同步]
            M4[配置审计]
        end
    end

    S1 --> L1
    S2 --> L2
    S3 --> L3
    S4 --> L4
    L1 --> ST1
    L2 --> ST2
    L3 --> ST3
    L4 --> ST4
    ST1 --> A1
    ST2 --> A2
    ST3 --> A3
    ST4 --> A4
    A1 --> M1
    A2 --> M2
    A3 --> M3
    A4 --> M4

    classDef sourceBox fill:#4caf50,color:#fff
    classDef loaderBox fill:#ff9800,color:#fff
    classDef storageBox fill:#2196f3,color:#fff
    classDef appBox fill:#9c27b0,color:#fff
    classDef monitorBox fill:#f44336,color:#fff

    class S1,S2,S3,S4 sourceBox
    class L1,L2,L3,L4 loaderBox
    class ST1,ST2,ST3,ST4 storageBox
    class A1,A2,A3,A4 appBox
    class M1,M2,M3,M4 monitorBox
```

## 📈 扩展性架构图

```mermaid
graph TB
    subgraph "水平扩展"
        H1[多服务器实例]
        H2[负载均衡]
        H3[服务发现]
        H4[集群管理]
    end

    subgraph "垂直扩展"
        V1[CPU优化]
        V2[内存优化]
        V3[网络优化]
        V4[存储优化]
    end

    subgraph "功能扩展"
        F1[插件系统]
        F2[API扩展]
        F3[协议扩展]
        F4[过滤器扩展]
    end

    subgraph "性能扩展"
        P1[连接池扩展]
        P2[缓存扩展]
        P3[异步处理]
        P4[批量处理]
    end

    H1 --> V1
    H2 --> V2
    H3 --> F1
    H4 --> F2
    V1 --> P1
    V2 --> P2
    F1 --> P3
    F2 --> P4

    classDef horizontalBox fill:#4caf50,color:#fff
    classDef verticalBox fill:#ff9800,color:#fff
    classDef functionalBox fill:#2196f3,color:#fff
    classDef performanceBox fill:#9c27b0,color:#fff

    class H1,H2,H3,H4 horizontalBox
    class V1,V2,V3,V4 verticalBox
    class F1,F2,F3,F4 functionalBox
    class P1,P2,P3,P4 performanceBox
```

## 📋 架构图说明

### 🎯 架构图用途

1. **整体系统架构图**: 展示完整的客户端-服务器架构和组件关系
2. **数据流架构图**: 说明请求处理的完整流程和决策路径
3. **组件层次架构图**: 展示系统的分层设计和职责划分
4. **技术栈架构图**: 说明使用的技术组件和依赖关系
5. **部署架构图**: 展示生产环境的部署方案和基础设施
6. **性能监控架构图**: 说明监控体系的数据流和处理流程
7. **安全架构图**: 展示多层安全防护体系
8. **配置管理架构图**: 说明配置管理的完整流程
9. **扩展性架构图**: 展示系统的扩展能力和扩展方向

### 🔧 如何使用这些架构图

#### 开发人员
- 使用**整体系统架构图**了解系统全貌
- 参考**组件层次架构图**理解代码结构
- 查看**技术栈架构图**了解技术选型

#### 运维人员
- 参考**部署架构图**进行环境部署
- 使用**性能监控架构图**搭建监控系统
- 查看**安全架构图**配置安全策略

#### 架构师
- 使用**数据流架构图**优化处理流程
- 参考**扩展性架构图**规划系统扩展
- 查看**配置管理架构图**设计配置策略

### 📊 关键指标

| 组件类型 | 关键指标 | 监控重点 |
|---------|---------|---------|
| 代理客户端 | 连接数、响应时间、错误率 | 用户体验 |
| 代理服务器 | 并发连接、吞吐量、资源使用率 | 性能稳定性 |
| 连接池 | 池使用率、连接复用率、超时率 | 资源效率 |
| 安全过滤 | 过滤命中率、误报率、处理延迟 | 安全效果 |
| 监控系统 | 数据完整性、告警及时性、存储容量 | 运维质量 |

### 🚀 扩展建议

#### 短期扩展 (1-3个月)
1. **Web管理界面**: 基于性能监控架构图实现
2. **API接口**: 扩展配置管理架构
3. **容器化部署**: 优化部署架构

#### 中期扩展 (3-6个月)
1. **微服务架构**: 拆分单体服务
2. **服务网格**: 引入Istio/Linkerd
3. **云原生部署**: Kubernetes集群

#### 长期扩展 (6-12个月)
1. **AI智能路由**: 机器学习优化路由决策
2. **边缘计算**: CDN节点部署
3. **多云部署**: 跨云厂商部署

### 📚 相关文档

- [CORE_ARCHITECTURE.md](CORE_ARCHITECTURE.md) - 核心架构详细说明
- [FEATURES.md](FEATURES.md) - 功能特性详解
- [README.md](README.md) - 项目总体介绍
- [proxy-server/](proxy-server/) - 服务器端实现
- [proxy-client/](proxy-client/) - 客户端实现

---

**注意**: 本架构图基于当前系统实现绘制，随着系统演进可能需要更新。建议定期review和更新架构图以保持文档的准确性。
