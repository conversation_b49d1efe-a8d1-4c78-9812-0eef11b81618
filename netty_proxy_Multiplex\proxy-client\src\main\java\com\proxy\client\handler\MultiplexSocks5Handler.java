package com.proxy.client.handler;

import com.proxy.client.config.ProxyClientConfigManager;
import com.proxy.client.connection.ConnectionManager;
import com.proxy.client.connection.DirectConnectionHandler;
import com.proxy.client.connection.SessionHandler;
import com.proxy.client.filter.AddressFilter;
import com.proxy.client.filter.DefaultAddressFilter;
import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.*;
import io.netty.channel.socket.DatagramPacket;
import io.netty.channel.socket.nio.NioDatagramChannel;
import io.netty.handler.codec.socksx.v5.Socks5AddressDecoder;
import io.netty.handler.codec.socksx.v5.Socks5AddressType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetSocketAddress;

/**
 * 基于多路复用的SOCKS5协议处理器
 * 通过ConnectionManager复用长连接处理SOCKS5请求
 */
public class MultiplexSocks5Handler extends ChannelInboundHandlerAdapter implements SessionHandler {
    private static final Logger logger = LoggerFactory.getLogger(MultiplexSocks5Handler.class);

    private static final byte SOCKS_VERSION = 0x05;
    private static final byte AUTH_METHOD_NO_AUTH = 0x00;
    private static final byte CMD_CONNECT = 0x01;
    private static final byte CMD_UDP_ASSOCIATE = 0x03;
    private static final byte ADDR_TYPE_IPV4 = 0x01;
    private static final byte SUCCESS = 0x00;

    private enum State {
        INIT,
        AUTH,
        CONNECT,
        RELAY
    }

    private State currentState = State.INIT;
    private ChannelHandlerContext clientContext;
    private int sessionId = -1;
    private String targetHost;
    private int targetPort;
    private boolean isDirect = false; // 标记是否为直连模式
    private boolean isUdpMode = false; // 标记是否为UDP模式
    private Channel udpChannel; // UDP通道
    private int udpLocalPort = 0; // UDP本地端口

    // 地址过滤器（从配置中获取过滤模式）
    private static final AddressFilter addressFilter = createAddressFilter();

    /**
     * 创建地址过滤器
     */
    private static AddressFilter createAddressFilter() {
        ProxyClientConfigManager configManager = ProxyClientConfigManager.getInstance();
        return new DefaultAddressFilter(configManager.getFilterMode());
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        this.clientContext = ctx;
        logger.debug("SOCKS5客户端连接建立");
        super.channelActive(ctx);
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        ByteBuf buffer = (ByteBuf) msg;

        try {
            switch (currentState) {
                case INIT:
                    handleInitialRequest(ctx, buffer);
                    break;
                case AUTH:
                    handleAuthRequest(ctx, buffer);
                    break;
                case CONNECT:
                    handleConnectRequest(ctx, buffer);
                    break;
                case RELAY:
                    if (isUdpMode) {
                        // UDP模式：处理UDP数据包
                        handleUdpData(ctx, buffer);
                    } else {
                        // TCP模式：根据连接类型转发数据
                        if (sessionId != -1) {
                            byte[] data = new byte[buffer.readableBytes()];
                            buffer.readBytes(data);

                            if (isDirect) {
                                // 直连模式：发送到直连处理器
                                DirectConnectionHandler.getInstance().sendData(sessionId, data);
                            } else {
                                // 代理模式：发送到代理服务器
                                ConnectionManager.getInstance().sendData(sessionId, data);
                            }
                        }
                    }
                    return;
            }
        } finally {
            buffer.release();
        }
    }

    private void handleInitialRequest(ChannelHandlerContext ctx, ByteBuf buffer) {
        if (buffer.readableBytes() < 2) {
            logger.warn("SOCKS5初始请求数据不足");
            ctx.close();
            return;
        }

        byte version = buffer.readByte();
        byte methodCount = buffer.readByte();

        if (version != SOCKS_VERSION) {
            logger.warn("不支持的SOCKS版本: {}", version);
            ctx.close();
            return;
        }

        if (buffer.readableBytes() < methodCount) {
            logger.warn("SOCKS5方法数据不足");
            ctx.close();
            return;
        }

        // 检查是否支持无认证方法
        boolean noAuthSupported = false;
        for (int i = 0; i < methodCount; i++) {
            byte method = buffer.readByte();
            if (method == AUTH_METHOD_NO_AUTH) {
                noAuthSupported = true;
            }
        }

        // 发送认证方法选择响应
        ByteBuf response = Unpooled.buffer(2);
        response.writeByte(SOCKS_VERSION);
        if (noAuthSupported) {
            response.writeByte(AUTH_METHOD_NO_AUTH);
            currentState = State.CONNECT;
            logger.debug("SOCKS5握手成功，选择无认证方法");
        } else {
            response.writeByte(0xFF); // 无可接受的方法
            logger.warn("客户端不支持无认证方法");
        }

        ctx.writeAndFlush(response);
        if (!noAuthSupported) {
            ctx.close();
        }
    }

    private void handleAuthRequest(ChannelHandlerContext ctx, ByteBuf buffer) {
        // 当前实现只支持无认证，此方法暂时不会被调用
        logger.debug("处理认证请求");
        currentState = State.CONNECT;
    }

    private void handleConnectRequest(ChannelHandlerContext ctx, ByteBuf buffer) {
        if (buffer.readableBytes() < 4) {
            logger.warn("SOCKS5连接请求数据不足");
            ctx.close();
            return;
        }

        byte version = buffer.readByte();
        byte cmd = buffer.readByte();
        buffer.readByte(); // reserved byte
        byte addrType = buffer.readByte();

        if (version != SOCKS_VERSION) {
            logger.warn("连接请求版本错误: {}", version);
            ctx.close();
            return;
        }

        if (cmd != CMD_CONNECT && cmd != CMD_UDP_ASSOCIATE) {
            logger.warn("不支持的命令: {}", cmd);
            sendConnectResponse(ctx, (byte) 0x07); // 不支持的命令
            return;
        }

        try {
            // 使用Netty内置API解析目标地址
            Socks5AddressType addressType = Socks5AddressType.valueOf(addrType);
            String decodedHost = Socks5AddressDecoder.DEFAULT.decodeAddress(addressType, buffer);
            int decodedPort = buffer.readUnsignedShort();

            targetHost = decodedHost;
            targetPort = decodedPort;

            if (cmd == CMD_UDP_ASSOCIATE) {
                // UDP ASSOCIATE 命令处理
                isUdpMode = true;
                logger.info("SOCKS5 UDP ASSOCIATE请求: {}:{}", targetHost, targetPort);
                handleUdpAssociate(ctx);
            } else {
                // TCP CONNECT 命令处理
                logger.info("SOCKS5连接请求: {}:{}", targetHost, targetPort);

                // 使用地址过滤器判断连接方式
                AddressFilter.ConnectionType connectionType = addressFilter.shouldUseProxy(targetHost, targetPort);

                if (connectionType == AddressFilter.ConnectionType.DIRECT) {
                    // 直连模式
                    isDirect = true;
                    sessionId = DirectConnectionHandler.getInstance().createDirectConnection(targetHost, targetPort,
                            this);
                    logger.info("使用直连模式: {}:{} (sessionId={})", targetHost, targetPort, sessionId);
                } else {
                    // 代理模式 - 使用TCP会话
                    isDirect = false;
                    sessionId = ConnectionManager.getInstance().createTcpSession(targetHost, targetPort, this);
                    logger.info("使用TCP代理模式: {}:{} (sessionId={})", targetHost, targetPort, sessionId);
                }
            }

        } catch (Exception e) {
            logger.error("处理连接请求异常", e);
            sendConnectResponse(ctx, (byte) 0x01); // 一般性SOCKS服务器故障
        }
    }

    private void handleUdpAssociate(ChannelHandlerContext ctx) {
        try {
            // 使用地址过滤器判断连接方式
            AddressFilter.ConnectionType connectionType = addressFilter.shouldUseProxy(targetHost, targetPort);

            if (connectionType == AddressFilter.ConnectionType.DIRECT) {
                // 直连模式：创建UDP socket用于直接转发
                createDirectUdpConnection(ctx);
            } else {
                // 代理模式：向后端完成认证并建立UDP代理连接
                createProxyUdpConnection(ctx);
            }

        } catch (Exception e) {
            logger.error("处理UDP ASSOCIATE请求失败", e);
            sendUdpAssociateResponse(ctx, (byte) 0x01, 0);
        }
    }

    private void createDirectUdpConnection(ChannelHandlerContext ctx) {
        try {
            // 创建UDP Bootstrap
            Bootstrap udpBootstrap = new Bootstrap();
            udpBootstrap.group(ctx.channel().eventLoop())
                    .channel(NioDatagramChannel.class)
                    .handler(new ChannelInboundHandlerAdapter() {
                        @Override
                        public void channelRead(ChannelHandlerContext udpCtx, Object msg) throws Exception {
                            if (msg instanceof DatagramPacket) {
                                DatagramPacket packet = (DatagramPacket) msg;
                                // 将接收到的UDP数据包转发回SOCKS5客户端
                                forwardUdpPacketToClient(packet);
                            }
                        }

                        @Override
                        public void exceptionCaught(ChannelHandlerContext udpCtx, Throwable cause) throws Exception {
                            logger.error("UDP直连通道异常", cause);
                        }
                    });

            // 绑定UDP端口
            ChannelFuture bindFuture = udpBootstrap.bind(0);
            bindFuture.addListener(new ChannelFutureListener() {
                @Override
                public void operationComplete(ChannelFuture future) throws Exception {
                    if (future.isSuccess()) {
                        udpChannel = future.channel();
                        InetSocketAddress localAddress = (InetSocketAddress) udpChannel.localAddress();
                        udpLocalPort = localAddress.getPort();

                        isDirect = true;
                        logger.info("UDP直连模式创建成功，本地端口: {}", udpLocalPort);

                        // 发送成功响应
                        sendUdpAssociateResponse(ctx, SUCCESS, udpLocalPort);
                    } else {
                        logger.error("UDP直连端口绑定失败", future.cause());
                        sendUdpAssociateResponse(ctx, (byte) 0x01, 0);
                    }
                }
            });

        } catch (Exception e) {
            logger.error("创建UDP直连失败", e);
            sendUdpAssociateResponse(ctx, (byte) 0x01, 0);
        }
    }

    private void createProxyUdpConnection(ChannelHandlerContext ctx) {
        try {
            isDirect = false;
            sessionId = ConnectionManager.getInstance().createUdpSession(targetHost, targetPort, this);

            // 创建本地UDP端口用于接收客户端数据
            createLocalUdpPort(ctx);

            logger.info("UDP代理模式复用TCP连接，sessionId: {}", sessionId);

        } catch (Exception e) {
            logger.error("创建UDP代理连接失败", e);
            sendUdpAssociateResponse(ctx, (byte) 0x01, 0);
        }
    }

    private void createLocalUdpPort(ChannelHandlerContext ctx) {
        try {
            Bootstrap udpBootstrap = new Bootstrap();
            udpBootstrap.group(ctx.channel().eventLoop())
                    .channel(NioDatagramChannel.class)
                    .handler(new ChannelInboundHandlerAdapter() {
                        @Override
                        public void channelRead(ChannelHandlerContext udpCtx, Object msg) throws Exception {
                            if (msg instanceof DatagramPacket) {
                                DatagramPacket packet = (DatagramPacket) msg;
                                // 将客户端UDP数据包转发到代理服务器
                                forwardUdpPacketToProxy(packet);
                            }
                        }
                    });

            ChannelFuture bindFuture = udpBootstrap.bind(0);
            bindFuture.addListener(future -> {
                if (future.isSuccess()) {
                    udpChannel = ((ChannelFuture) future).channel();
                    InetSocketAddress localAddress = (InetSocketAddress) udpChannel.localAddress();
                    udpLocalPort = localAddress.getPort();

                    logger.info("UDP代理本地端口创建成功: {}", udpLocalPort);
                    sendUdpAssociateResponse(ctx, SUCCESS, udpLocalPort);
                } else {
                    logger.error("UDP代理本地端口绑定失败", ((ChannelFuture) future).cause());
                    sendUdpAssociateResponse(ctx, (byte) 0x01, 0);
                }
            });

        } catch (Exception e) {
            logger.error("创建UDP代理本地端口失败", e);
            sendUdpAssociateResponse(ctx, (byte) 0x01, 0);
        }
    }

    private void sendUdpAssociateResponse(ChannelHandlerContext ctx, byte status, int udpPort) {
        ByteBuf response = Unpooled.buffer(10);
        response.writeByte(SOCKS_VERSION);
        response.writeByte(status);
        response.writeByte(0x00); // 保留字段
        response.writeByte(ADDR_TYPE_IPV4);
        response.writeInt(0); // 绑定地址 0.0.0.0
        response.writeShort(udpPort); // UDP端口

        ctx.writeAndFlush(response);

        if (status == SUCCESS) {
            currentState = State.RELAY;
            logger.debug("SOCKS5 UDP ASSOCIATE响应已发送，切换到UDP中继模式");
        } else {
            ctx.close();
        }
    }

    private void handleUdpData(ChannelHandlerContext ctx, ByteBuf buffer) {
        // UDP数据包格式：
        // +----+------+------+----------+----------+----------+
        // |RSV | FRAG | ATYP | DST.ADDR | DST.PORT | DATA |
        // +----+------+------+----------+----------+----------+
        // | 2 | 1 | 1 | Variable | 2 | Variable |
        // +----+------+------+----------+----------+----------+

        if (buffer.readableBytes() < 4) {
            logger.warn("UDP数据包头部不完整");
            return;
        }

        try {
            buffer.readShort(); // RSV字段 - 跳过保留字段
            byte frag = buffer.readByte(); // FRAG字段
            byte addrType = buffer.readByte(); // ATYP字段

            if (frag != 0) {
                logger.warn("不支持UDP分片: {}", frag);
                return;
            }

            // 解析目标地址
            Socks5AddressType addressType = Socks5AddressType.valueOf(addrType);
            String destHost = Socks5AddressDecoder.DEFAULT.decodeAddress(addressType, buffer);
            int destPort = buffer.readUnsignedShort();

            // 读取实际数据
            byte[] udpData = new byte[buffer.readableBytes()];
            buffer.readBytes(udpData);

            logger.debug("收到UDP数据包: {}:{}, 数据长度: {}", destHost, destPort, udpData.length);

            // 这里应该将UDP数据转发到目标地址
            // 实际实现中需要根据是否使用代理来决定转发方式
            forwardUdpData(destHost, destPort, udpData);

        } catch (Exception e) {
            logger.error("处理UDP数据包异常", e);
        }
    }

    private void forwardUdpData(String destHost, int destPort, byte[] data) {
        if (isDirect) {
            // 直连模式：直接发送UDP数据包
            try {
                InetSocketAddress destAddress = new InetSocketAddress(destHost, destPort);
                ByteBuf buffer = Unpooled.wrappedBuffer(data);
                DatagramPacket packet = new DatagramPacket(buffer, destAddress);

                if (udpChannel != null && udpChannel.isActive()) {
                    udpChannel.writeAndFlush(packet);
                    logger.debug("UDP直连转发: {}:{}, 数据长度: {}", destHost, destPort, data.length);
                }
            } catch (Exception e) {
                logger.error("UDP直连转发失败: {}:{}", destHost, destPort, e);
            }
        } else {
            // 代理模式：复用TCP连接，通过特殊格式标识UDP数据包
            try {
                if (sessionId != -1) {
                    // 构造UDP数据包格式：[HOST_LEN][HOST][PORT][DATA_LEN][DATA]
                    ByteBuf udpPacket = Unpooled.buffer();

                    // 写入目标主机信息
                    byte[] hostBytes = destHost.getBytes("UTF-8");
                    udpPacket.writeByte(hostBytes.length);
                    udpPacket.writeBytes(hostBytes);
                    udpPacket.writeShort(destPort);

                    // 写入数据长度和数据
                    udpPacket.writeShort(data.length);
                    udpPacket.writeBytes(data);

                    // 转换为字节数组发送
                    byte[] packetData = new byte[udpPacket.readableBytes()];
                    udpPacket.readBytes(packetData);
                    udpPacket.release();

                    // 使用UDP数据包类型发送
                    ConnectionManager.getInstance().sendUdpData(sessionId, packetData);
                    logger.debug("UDP代理转发(复用TCP): {}:{}, 数据长度: {}", destHost, destPort, data.length);
                }
            } catch (Exception e) {
                logger.error("UDP代理转发失败: {}:{}", destHost, destPort, e);
            }
        }
    }

    private void forwardUdpPacketToClient(DatagramPacket packet) {
        try {
            // 将UDP响应数据包转发回SOCKS5客户端
            InetSocketAddress sender = packet.sender();
            ByteBuf content = packet.content();

            // 构造SOCKS5 UDP响应格式
            ByteBuf response = Unpooled.buffer();
            response.writeShort(0); // RSV
            response.writeByte(0); // FRAG
            response.writeByte(ADDR_TYPE_IPV4); // ATYP

            // 写入发送方地址
            byte[] addressBytes = sender.getAddress().getAddress();
            response.writeBytes(addressBytes);
            response.writeShort(sender.getPort());

            // 写入数据
            response.writeBytes(content);

            // 发送给客户端
            if (clientContext != null && clientContext.channel().isActive()) {
                clientContext.writeAndFlush(response);
            }

        } catch (Exception e) {
            logger.error("转发UDP响应到客户端失败", e);
        }
    }

    private void forwardUdpPacketToProxy(DatagramPacket packet) {
        try {
            // 解析SOCKS5 UDP数据包格式并转发到代理服务器
            ByteBuf content = packet.content();

            if (content.readableBytes() < 4) {
                return;
            }

            content.readShort(); // RSV
            content.readByte(); // FRAG
            byte addrType = content.readByte(); // ATYP

            // 解析目标地址
            Socks5AddressType addressType = Socks5AddressType.valueOf(addrType);
            String destHost = Socks5AddressDecoder.DEFAULT.decodeAddress(addressType, content);
            int destPort = content.readUnsignedShort();

            // 读取数据
            byte[] data = new byte[content.readableBytes()];
            content.readBytes(data);

            // 通过复用的TCP连接转发UDP数据
            if (sessionId != -1) {
                // 构造UDP数据包格式：[HOST_LEN][HOST][PORT][DATA_LEN][DATA]
                ByteBuf udpPacket = Unpooled.buffer();

                // 写入目标主机信息
                byte[] hostBytes = destHost.getBytes("UTF-8");
                udpPacket.writeByte(hostBytes.length);
                udpPacket.writeBytes(hostBytes);
                udpPacket.writeShort(destPort);

                // 写入数据长度和数据
                udpPacket.writeShort(data.length);
                udpPacket.writeBytes(data);

                // 转换为字节数组发送
                byte[] packetData = new byte[udpPacket.readableBytes()];
                udpPacket.readBytes(packetData);
                udpPacket.release();

                // 使用UDP数据包类型发送
                ConnectionManager.getInstance().sendUdpData(sessionId, packetData);
            }

        } catch (Exception e) {
            logger.error("转发UDP数据包到代理失败", e);
        }
    }

    private void sendConnectResponse(ChannelHandlerContext ctx, byte status) {
        ByteBuf response = Unpooled.buffer(10);
        response.writeByte(SOCKS_VERSION);
        response.writeByte(status);
        response.writeByte(0x00); // 保留字段
        response.writeByte(ADDR_TYPE_IPV4);
        response.writeInt(0); // 绑定地址 0.0.0.0
        response.writeShort(0); // 绑定端口 0

        ctx.writeAndFlush(response);

        if (status == SUCCESS) {
            currentState = State.RELAY;
            logger.debug("SOCKS5连接成功响应已发送，切换到透明转发模式");
        } else {
            ctx.close();
        }
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        // 关闭UDP通道
        if (udpChannel != null && udpChannel.isActive()) {
            udpChannel.close();
        }

        if (sessionId != -1) {
            if (isUdpMode) {
                if (isDirect) {
                    // UDP直连模式：关闭直连会话
                    DirectConnectionHandler.getInstance().closeConnection(sessionId);
                } else {
                    // UDP代理模式：复用TCP连接，使用相同的关闭方法
                    ConnectionManager.getInstance().closeSession(sessionId);
                }
            } else {
                if (isDirect) {
                    // TCP直连模式：关闭直连会话
                    DirectConnectionHandler.getInstance().closeConnection(sessionId);
                } else {
                    // TCP代理模式：关闭代理会话
                    ConnectionManager.getInstance().closeSession(sessionId);
                }
            }
        }
        super.channelInactive(ctx);
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        logger.error("SOCKS5处理异常", cause);
        ctx.close();
    }

    // SessionHandler 接口实现

    @Override
    public void onConnectResponse(boolean success, int serverSessionId) {
        if (success) {
            // 更新为服务器分配的sessionId
            this.sessionId = serverSessionId;
            sendConnectResponse(clientContext, SUCCESS);
            logger.debug("SOCKS5连接建立成功，sessionId更新为: {}", serverSessionId);
        } else {
            sendConnectResponse(clientContext, (byte) 0x05); // 连接被拒绝
            logger.error("SOCKS5连接建立失败: {}:{} (sessionId={})", targetHost, targetPort, sessionId);
        }
    }

    @Override
    public void onData(byte[] data) {
        if (clientContext != null && clientContext.channel().isActive() && data.length > 0) {
            // TCP模式：直接转发
            ByteBuf buffer = clientContext.alloc().buffer(data.length);
            buffer.writeBytes(data);
            clientContext.writeAndFlush(buffer);
        }
    }

    /**
     * 处理UDP数据回调（新增方法）
     */
    @Override
    public void onUdpData(byte[] data) {
        if (clientContext != null && clientContext.channel().isActive() && data.length > 0) {
            if (isUdpMode && !isDirect) {
                // UDP代理模式：处理UDP响应数据包
                handleUdpResponseFromProxy(data);
            }
        }
    }

    private void handleUdpResponseFromProxy(byte[] data) {
        try {
            ByteBuf buffer = Unpooled.wrappedBuffer(data);

            // 读取源主机信息
            int hostLen = buffer.readUnsignedByte();
            byte[] hostBytes = new byte[hostLen];
            buffer.readBytes(hostBytes);
            String sourceHost = new String(hostBytes, "UTF-8");
            int sourcePort = buffer.readUnsignedShort();

            // 读取数据长度和数据
            int dataLen = buffer.readUnsignedShort();
            byte[] udpData = new byte[dataLen];
            buffer.readBytes(udpData);

            // 构造SOCKS5 UDP响应格式发送给客户端
            ByteBuf response = Unpooled.buffer();
            response.writeShort(0); // RSV
            response.writeByte(0); // FRAG
            response.writeByte(ADDR_TYPE_IPV4); // ATYP

            // 写入源地址（简化处理，使用0.0.0.0）
            response.writeInt(0); // 源IP地址
            response.writeShort(sourcePort); // 源端口

            // 写入数据
            response.writeBytes(udpData);

            // 发送给客户端
            if (clientContext != null && clientContext.channel().isActive()) {
                clientContext.writeAndFlush(response);
            }

            logger.debug("UDP代理响应转发给客户端: {}:{}, 数据长度: {}", sourceHost, sourcePort, udpData.length);

        } catch (Exception e) {
            logger.error("处理UDP代理响应失败", e);
        }
    }

    @Override
    public void onClose() {
        if (clientContext != null && clientContext.channel().isActive()) {
            clientContext.close();
        }
    }

    @Override
    public void close() {
        if (clientContext != null && clientContext.channel().isActive()) {
            clientContext.close();
        }
    }
}
