package com.proxy.server.protocol;

import com.proxy.server.handler.MultiplexProxyHandler;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * 协议检测器
 * 检测连接使用的协议类型（HTTP代理 vs 多路复用协议）
 */
public class MultiplexProtocolDetector extends ByteToMessageDecoder {
    private static final Logger logger = LoggerFactory.getLogger(MultiplexProtocolDetector.class);

    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf in, List<Object> out) throws Exception {
        if (in.readableBytes() < 2) {
            // 需要至少2个字节来检测协议
            logger.debug("数据不足，等待更多数据进行协议检测: {} bytes", in.readableBytes());
            return;
        }

        // 标记当前读取位置
        in.markReaderIndex();

        // 读取前两个字节检查是否为多路复用协议魔数
        short magic = in.readShort();

        // 重置读取位置
        in.resetReaderIndex();

        logger.debug("协议检测: magic=0x{}, 期望=0x{}", Integer.toHexString(magic & 0xFFFF), Integer.toHexString(MultiplexProtocol.MAGIC & 0xFFFF));

        if (magic == MultiplexProtocol.MAGIC) {
            // 多路复用协议
            logger.info("检测到多路复用协议连接: {}", ctx.channel().remoteAddress());

            // 移除协议检测器，添加多路复用处理器
            ctx.pipeline().remove(this);
            MultiplexProxyHandler multiplexHandler = new MultiplexProxyHandler();
            ctx.pipeline().addLast("multiplexHandler", multiplexHandler);

            // 手动触发channelActive事件，因为新添加的处理器错过了这个事件
            multiplexHandler.channelActive(ctx);

            // 将缓冲区中的所有数据传递给新的处理器
            if (in.isReadable()) {
                ByteBuf remainingData = in.readBytes(in.readableBytes());
                // 直接调用新处理器的channelRead方法处理剩余数据
                multiplexHandler.channelRead(ctx, remainingData);
            }
        } else {
            // 不是多路复用协议，将数据传递给下一个处理器
            if (in.isReadable()) {
                out.add(in.readBytes(in.readableBytes()));
            }
        }
    }
}
