package com.proxy.server;

import com.proxy.server.core.ProxyServerInitializer;
import com.proxy.server.pool.ConnectionPool;
import com.proxy.server.config.ConnectionPoolConfig;
import com.proxy.server.config.ProxyServerConfigManager;
import com.proxy.server.config.properties.ProxyServerProperties;
import com.proxy.server.metrics.PerformanceMetrics;
import com.proxy.server.ssl.SslContextManager;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.buffer.PooledByteBufAllocator;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelOption;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.WriteBufferWaterMark;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.util.concurrent.DefaultThreadFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;

/**
 * 代理服务器主类
 * 接收来自代理客户端的请求，转发给目标服务器
 */
public class ProxyServer {
    private static final Logger logger = LoggerFactory.getLogger(ProxyServer.class);

    private final int port;
    private ScheduledExecutorService monitoringExecutor;
    
    public ProxyServer(int port) {
        this.port = port;
    }
    
    public void start() throws InterruptedException {
        // Get configuration
        ProxyServerConfigManager configManager = ProxyServerConfigManager.getInstance();
        ProxyServerProperties.PerformanceProperties perfConfig = configManager.getProperties().getPerformance();
        
        // Configure boss thread count
        int bossThreads = perfConfig.getBossThreads();
        if (bossThreads <= 0) {
            bossThreads = 1; // Default to 1 boss thread
        }
        
        // Configure worker thread count
        int workerThreads = perfConfig.getWorkerThreads();
        if (workerThreads <= 0) {
            // Automatically calculate thread count based on CPU cores
            workerThreads = Math.max(4, Runtime.getRuntime().availableProcessors() * 2);
        }
        
        // Create thread factories with meaningful names
        ThreadFactory bossThreadFactory = new DefaultThreadFactory("proxy-server-boss");
        ThreadFactory workerThreadFactory = new DefaultThreadFactory("proxy-server-worker");
        
        // Create event loop groups with configurable thread counts
        EventLoopGroup bossGroup = new NioEventLoopGroup(bossThreads, bossThreadFactory);
        EventLoopGroup workerGroup = new NioEventLoopGroup(workerThreads, workerThreadFactory);

        try {
            ServerBootstrap bootstrap = new ServerBootstrap();
            bootstrap.group(bossGroup, workerGroup)
                    .channel(NioServerSocketChannel.class)
                    .childHandler(new ProxyServerInitializer())
                    // Netty性能优化选项
                    .option(ChannelOption.SO_BACKLOG, 1024)
                    .option(ChannelOption.SO_REUSEADDR, true)
                    .option(ChannelOption.ALLOCATOR, PooledByteBufAllocator.DEFAULT)
                    .childOption(ChannelOption.TCP_NODELAY, true)
                    .childOption(ChannelOption.SO_KEEPALIVE, true)
                    .childOption(ChannelOption.SO_REUSEADDR, true)
                    .childOption(ChannelOption.ALLOCATOR, PooledByteBufAllocator.DEFAULT)
                    .childOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 10000)
                    .childOption(ChannelOption.SO_RCVBUF, 65536)
                    .childOption(ChannelOption.SO_SNDBUF, 65536)
                    .childOption(ChannelOption.WRITE_BUFFER_WATER_MARK, new WriteBufferWaterMark(8 * 1024, 32 * 1024));

            ChannelFuture future = bootstrap.bind(port).sync();
            logger.info("代理服务器启动成功，监听端口: {}，Boss线程数: {}，Worker线程数: {}", 
                       port, bossThreads, workerThreads);

            // Show SSL configuration information
            SslContextManager sslManager = SslContextManager.getInstance();
            logger.info("SSL配置: {}", sslManager.getSslConfigSummary());

            // Enable connection pool functionality
            if (ConnectionPoolConfig.ENABLE_CONNECTION_POOL) {
                ConnectionPool.getInstance().start();
                logger.info("连接池功能已启用，最大连接数/主机: {}, 清理间隔: {}ms",
                    ConnectionPoolConfig.MAX_CONNECTIONS_PER_HOST,
                    ConnectionPoolConfig.POOL_CLEANUP_INTERVAL);
            }

            // Start performance monitoring task
            startPerformanceMonitoring();

            // Add shutdown hook
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                logger.info("正在关闭代理服务器...");
                ConnectionPool.getInstance().shutdown();
                if (monitoringExecutor != null) {
                    monitoringExecutor.shutdown();
                }
            }));

            future.channel().closeFuture().sync();
        } finally {
            ConnectionPool.getInstance().shutdown();
            if (monitoringExecutor != null) {
                monitoringExecutor.shutdown();
            }
            workerGroup.shutdownGracefully();
            bossGroup.shutdownGracefully();
        }
    }

    /**
     * 启动性能监控任务
     */
    private void startPerformanceMonitoring() {
        ProxyServerConfigManager configManager = ProxyServerConfigManager.getInstance();

        // Check if performance monitoring is enabled
        if (!configManager.isMetricsEnabled()) {
            logger.info("性能监控已禁用");
            return;
        }

        monitoringExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "PerformanceMonitor");
            t.setDaemon(true);
            return t;
        });

        // Use interval from configuration file
        int intervalSeconds = configManager.getMetricsReportIntervalSeconds();
        monitoringExecutor.scheduleWithFixedDelay(() -> {
            try {
                PerformanceMetrics.getInstance().logMetrics();
            } catch (Exception e) {
                logger.warn("性能监控任务执行异常: {}", e.getMessage());
            }
        }, intervalSeconds, intervalSeconds, TimeUnit.SECONDS);

        logger.info("性能监控任务已启动，每{}秒输出一次统计信息", intervalSeconds);
    }
    
    public static void main(String[] args) throws InterruptedException {
        // Parse command line arguments
        parseCommandLineArgs(args);

        // Initialize all configuration files and components at startup
        initializeComponents();

        // Get default port from configuration manager
        ProxyServerConfigManager configManager = ProxyServerConfigManager.getInstance();
        int port = configManager.getServerPort();

        // Command line arguments can override the port in the configuration file
        if (args.length > 0) {
            try {
                // If the first argument is a number, use it as the port number
                port = Integer.parseInt(args[0]);
            } catch (NumberFormatException e) {
                // If the first argument is not a number, it might be a config file path, port number might be in the second argument
                if (args.length > 1) {
                    try {
                        port = Integer.parseInt(args[1]);
                    } catch (NumberFormatException ex) {
                        // Keep using the port from the configuration file
                    }
                }
            }
        }

        new ProxyServer(port).start();
    }

    /**
     * 启动时初始化所有组件和配置文件
     */
    private static void initializeComponents() {
        logger.info("开始初始化服务器组件和配置文件...");

        try {
            // 1. Initialize GeoIPUtil（触发IP段数据加载和在线更新）
            logger.info("初始化地理位置IP判断工具...");
            com.proxy.server.util.GeoIPUtil.getInstance();

            // 2. Initialize GeoLocationFilter（触发恶意内容和白名单加载）
            logger.info("初始化地理位置过滤器...");
            com.proxy.server.filter.GeoLocationFilter.getInstance();

            logger.info("服务器组件和配置文件初始化完成");

        } catch (Exception e) {
            logger.error("初始化服务器组件失败", e);
            // Don't exit because of initialization failure, let the server continue to start
            logger.warn("将使用默认配置继续启动服务器");
        }
    }

    /**
     * 解析命令行参数
     * 支持的格式：
     * - java ProxyServer [port]
     * - java ProxyServer --config=path/to/config.yml [port]
     * - java ProxyServer -c path/to/config.yml [port]
     */
    private static void parseCommandLineArgs(String[] args) {
        for (int i = 0; i < args.length; i++) {
            String arg = args[i];

            // Handle --config=path format
            if (arg.startsWith("--config=")) {
                String configPath = arg.substring("--config=".length());
                ProxyServerConfigManager.setConfigFilePath(configPath);
                continue;
            }

            // Handle -c path format
            if (arg.equals("-c") && i + 1 < args.length) {
                String configPath = args[i + 1];
                ProxyServerConfigManager.setConfigFilePath(configPath);
                i++; // Skip the next argument, because it is the config file path
                continue;
            }
        }
    }
}