# SSL/TLS 部署指南

本指南详细说明如何在不同环境中配置和部署SSL/TLS功能。

## 🚀 快速开始

### 1. 生成开发测试证书

**Windows:**
```bash
cd scripts
.\generate-ssl-certificates.bat
```

**Linux/Mac:**
```bash
cd scripts
chmod +x generate-ssl-certificates.sh
./generate-ssl-certificates.sh
```

脚本会自动生成以下文件：
- `server.p12` - 服务器证书 (CN=localhost)
- `client.p12` - 客户端证书 (用于双向认证)
- `truststore.p12` - 信任库 (包含服务器和客户端证书)
- 证书会自动复制到相应的 resources 目录

### 2. 配置SSL

**使用默认配置（推荐）:**
默认配置文件已经启用SSL，可以直接使用。

**使用预设配置模板:**
```bash
# 开发环境配置
cp configs/development/proxy-server-dev-ssl.yml proxy-server/src/main/resources/proxy-server.yml
cp configs/development/proxy-client-dev-ssl.yml proxy-client/src/main/resources/proxy-client.yml

# 生产环境配置
cp configs/production/proxy-server-prod-ssl.yml proxy-server/src/main/resources/proxy-server.yml
cp configs/production/proxy-client-prod-ssl.yml proxy-client/src/main/resources/proxy-client.yml
```

### 3. 启动SSL服务

**启动服务器:**
```bash
cd proxy-server
mvn exec:java -Dexec.mainClass="com.proxy.server.ProxyServer"
```

**启动客户端:**
```bash
cd proxy-client
mvn exec:java -Dexec.mainClass="com.proxy.client.Socks5ProxyClient"
```

## 📋 环境配置

### 开发环境

**特点:**
- 使用自签名证书
- 信任所有证书
- 禁用主机名验证
- 单向SSL认证
- 简化配置

**配置文件:**
- `configs/development/proxy-server-dev-ssl.yml`
- `configs/development/proxy-client-dev-ssl.yml`

**证书:**
- `server.p12` - 服务器证书
- `client.p12` - 客户端证书（可选）
- `truststore.p12` - 信任库

### 生产环境

**特点:**
- 使用CA签名证书
- 严格证书验证
- 启用主机名验证
- 双向SSL认证
- 高安全配置

**配置文件:**
- `configs/production/proxy-server-prod-ssl.yml`
- `configs/production/proxy-client-prod-ssl.yml`

**环境变量:**
```bash
export SSL_KEYSTORE_PASSWORD="your_secure_password"
export SSL_TRUSTSTORE_PASSWORD="your_secure_password"
export PROXY_AUTH_USERNAME="your_username"
export PROXY_AUTH_PASSWORD="your_secure_password"
export PROXY_SERVER_HOST="proxy.example.com"
```

## 🔧 证书管理

### 生成生产环境证书

1. **生成私钥和证书签名请求 (CSR)**
```bash
# 生成服务器私钥
openssl genrsa -out server.key 2048

# 生成CSR
openssl req -new -key server.key -out server.csr \
  -subj "/C=CN/ST=Beijing/L=Beijing/O=YourCompany/OU=IT/CN=proxy.example.com"

# 发送CSR给CA进行签名
```

2. **将CA签名的证书导入PKCS12格式**
```bash
# 假设收到了server.crt和ca-chain.crt
openssl pkcs12 -export -out server.p12 \
  -inkey server.key \
  -in server.crt \
  -certfile ca-chain.crt \
  -name "server"
```

3. **创建信任库**
```bash
# 导入CA根证书到信任库
keytool -importcert -alias ca-root \
  -keystore truststore.p12 \
  -storetype PKCS12 \
  -file ca-root.crt \
  -noprompt
```

### 证书更新流程

1. **检查证书有效期**
```bash
keytool -list -v -keystore server.p12 -storetype PKCS12
```

2. **更新证书**
```bash
# 备份旧证书
cp server.p12 server.p12.backup

# 导入新证书
keytool -importkeystore \
  -srckeystore new-server.p12 \
  -destkeystore server.p12 \
  -srcstoretype PKCS12 \
  -deststoretype PKCS12
```

3. **重启服务**
```bash
# 重启服务器和客户端以加载新证书
```

## 🛡️ 安全最佳实践

### 证书安全
- 使用强密码保护私钥
- 定期更新证书（建议每年）
- 使用2048位或更高的RSA密钥
- 考虑使用ECC证书以获得更好的性能

### 协议安全
- 只使用TLSv1.2和TLSv1.3
- 禁用不安全的密码套件
- 启用Perfect Forward Secrecy (PFS)

### 运维安全
- 证书密码通过环境变量配置
- 限制证书文件的访问权限
- 监控证书有效期
- 定期安全审计

## 🔍 故障排除

### 常见问题

1. **证书验证失败**
```
javax.net.ssl.SSLHandshakeException: PKIX path building failed
```
**解决方案:** 检查信任库配置，确保包含正确的CA证书

2. **主机名验证失败**
```
javax.net.ssl.SSLPeerUnverifiedException: Hostname verification failed
```
**解决方案:** 确保证书CN或SAN包含正确的主机名

3. **密码错误**
```
java.security.UnrecoverableKeyException: Cannot recover key
```
**解决方案:** 检查密钥库密码配置

### 调试SSL连接

1. **启用SSL调试日志**
```bash
java -Djavax.net.debug=ssl:handshake:verbose -jar your-app.jar
```

2. **使用OpenSSL测试**
```bash
# 测试服务器SSL
openssl s_client -connect localhost:8888 -servername localhost

# 查看证书信息
openssl s_client -connect localhost:8888 -showcerts
```

3. **检查证书链**
```bash
keytool -list -v -keystore server.p12 -storetype PKCS12
```

## 📊 监控和维护

### 证书监控
- 监控证书有效期
- 检查证书链完整性
- 监控SSL握手成功率
- 记录SSL错误日志

### 性能监控
- SSL握手时间
- 加密/解密性能
- 连接建立时间
- 内存使用情况

### 定期维护
- 每月检查证书有效期
- 每季度更新密码套件配置
- 每年更新证书
- 定期安全扫描
