#!/bin/bash
echo "Building proxy-client native executable..."

# Check if GraalVM is installed
if ! command -v native-image &> /dev/null; then
    echo "Error: native-image command not found. Please install GraalVM and add it to PATH."
    echo "Download GraalVM from: https://www.graalvm.org/downloads/"
    echo "After installation, run: gu install native-image"
    exit 1
fi

# Clean and compile the project
echo "Cleaning and compiling project..."
mvn clean compile

if [ $? -ne 0 ]; then
    echo "Error: Maven compilation failed"
    exit 1
fi

# Build native image using Maven plugin
echo "Building native image..."
mvn -Pnative native:compile

if [ $? -ne 0 ]; then
    echo "Error: Native image build failed"
    exit 1
fi

echo "Native executable built successfully!"
echo "Location: target/proxy-client"
echo ""
echo "Usage: ./target/proxy-client [local_port] [proxy_server_host] [proxy_server_port]"
echo "Example: ./target/proxy-client 1080 localhost 8888"
