# 配置文件示例

本文档提供了完整的配置文件示例，帮助您快速配置代理系统。

## 📁 配置文件结构

```
netty_proxy_Multiplex/
├── configs/
│   ├── development/
│   │   ├── proxy-server.yml      # 开发环境服务器配置
│   │   └── proxy-client.yml      # 开发环境客户端配置
│   └── production/
│       ├── proxy-server.yml      # 生产环境服务器配置
│       └── proxy-client.yml      # 生产环境客户端配置
├── proxy-server.yml              # 默认服务器配置
├── proxy-client.yml              # 默认客户端配置
├── proxy-server/src/main/resources/application.properties
└── proxy-client/src/main/resources/application.properties
```

## 🖥️ Proxy Server 配置示例

### 开发环境配置 (configs/development/proxy-server.yml)
```yaml
# 代理服务器配置文件

# 服务器配置
server:
  port: 8888

# 认证配置
auth:
  enable: true
  username: admin
  password: password11
  timeout:
    seconds: 30

# 连接池配置
pool:
  enable: true
  max-connections:
    per-host: 20
  idle-timeout:
    seconds: 60
  cleanup-interval:
    seconds: 30

# 性能监控配置
metrics:
  enable: true
  report:
    interval:
      seconds: 300

# 黑名单配置
blacklist:
  enable: true
  failure:
    threshold: 3
  cache:
    timeout:
      seconds: 180

# SSL/TLS配置
ssl:
  enable: false
  key-store-path: "server.p12"
  key-store-password: "xiang1"
  protocols:
    - "TLSv1.2"
    - "TLSv1.3"
```

### 生产环境配置 (configs/production/proxy-server.yml)
```yaml
# 生产环境代理服务器配置

server:
  port: 8888

auth:
  enable: true
  username: prod_admin
  password: secure_production_password
  timeout:
    seconds: 60

pool:
  enable: true
  max-connections:
    per-host: 50
  idle-timeout:
    seconds: 300
  cleanup-interval:
    seconds: 60

metrics:
  enable: true
  report:
    interval:
      seconds: 600

blacklist:
  enable: true
  failure:
    threshold: 5
  cache:
    timeout:
      seconds: 600

ssl:
  enable: true
  key-store-path: "production-server.p12"
  key-store-password: "production_ssl_password"
  protocols:
    - "TLSv1.2"
    - "TLSv1.3"
  client-auth: false
```

## 💻 Proxy Client 配置示例

### 开发环境配置 (configs/development/proxy-client.yml)
```yaml
# 代理客户端配置文件

# 地址过滤模式
filter:
  mode: ALL_PROXY

# 代理服务器配置
proxy:
  server:
    host: localhost
    port: 8888

# 认证配置
auth:
  enable: true
  username: admin
  password: password11
  timeout:
    seconds: 30

# 接入器配置
inbound:
  # SOCKS5 接入器列表
  socks5:
    - name: "socks5-main"
      port: 1081
      enabled: true
      description: "主要SOCKS5代理"
    - name: "socks5-backup"
      port: 1083
      enabled: false
      description: "备用SOCKS5代理"
  
  # HTTP 接入器列表
  http:
    - name: "http-main"
      port: 1082
      enabled: true
      description: "主要HTTP代理"
    - name: "http-backup"
      port: 1085
      enabled: false
      description: "备用HTTP代理"

# SSL/TLS配置
ssl:
  enable: false
  trust-all: true
  verify-hostname: false
```

### 生产环境配置 (configs/production/proxy-client.yml)
```yaml
# 生产环境代理客户端配置

filter:
  mode: CHINA_DIRECT

proxy:
  server:
    host: production-proxy-server.example.com
    port: 8888

auth:
  enable: true
  username: prod_admin
  password: secure_production_password
  timeout:
    seconds: 60

inbound:
  socks5:
    - name: "socks5-production"
      port: 1081
      enabled: true
      description: "生产SOCKS5代理"
  
  http:
    - name: "http-production"
      port: 1082
      enabled: true
      description: "生产HTTP代理"

ssl:
  enable: true
  trust-all: false
  trust-store-path: "production-truststore.p12"
  trust-store-password: "production_ssl_password"
  verify-hostname: true
```

## ⚙️ Properties 配置文件

### proxy-server/src/main/resources/application.properties
```properties
# Proxy Server Configuration Properties
# YAML配置文件路径
config.file.path=configs/development/proxy-server.yml
```

### proxy-client/src/main/resources/application.properties
```properties
# Proxy Client Configuration Properties
# YAML配置文件路径
config.file.path=configs/development/proxy-client.yml
```

## 🚀 使用示例

### 开发环境启动
```bash
# 启动服务器（使用开发配置）
cd proxy-server
mvn exec:java -Dexec.mainClass="com.proxy.server.ProxyServer"

# 启动客户端（使用开发配置）
cd proxy-client
mvn exec:java -Dexec.mainClass="com.proxy.client.ProxyClient"
```

### 生产环境启动
```bash
# 启动服务器（使用生产配置）
cd proxy-server
mvn exec:java -Dexec.mainClass="com.proxy.server.ProxyServer" -Dexec.args="--config=configs/production/proxy-server.yml"

# 启动客户端（使用生产配置）
cd proxy-client
mvn exec:java -Dexec.mainClass="com.proxy.client.ProxyClient" -Dexec.args="--config=configs/production/proxy-client.yml"
```

### 自定义配置启动
```bash
# 使用环境变量
export PROXY_SERVER_CONFIG=my-custom-server.yml
export PROXY_CLIENT_CONFIG=my-custom-client.yml

# 启动程序
mvn -f proxy-server/pom.xml exec:java -Dexec.mainClass="com.proxy.server.ProxyServer"
mvn -f proxy-client/pom.xml exec:java -Dexec.mainClass="com.proxy.client.ProxyClient"
```

## 🔗 相关文档

- [CONFIGURATION_GUIDE.md](CONFIGURATION_GUIDE.md) - 详细配置指南
- [README.md](README.md) - 项目主文档
- [SSL_CONFIGURATION_REFERENCE.md](SSL_CONFIGURATION_REFERENCE.md) - SSL配置参考
