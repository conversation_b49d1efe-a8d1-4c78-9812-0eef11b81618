package com.proxy.client.filter;

/**
 * 地址过滤器接口
 * 用于决定连接请求的转发方式
 */
public interface AddressFilter {
    
    /**
     * 过滤模式枚举
     */
    enum FilterMode {
        /** 所有连接都通过proxy-server转发 */
        ALL_PROXY,
        /** 中国地区IP直连，其他通过proxy-server转发 */
        CHINA_DIRECT,
        /** 所有连接都直连 */
        ALL_DIRECT
    }
    
    /**
     * 连接方式枚举
     */
    enum ConnectionType {
        /** 通过proxy-server转发 */
        PROXY,
        /** 直接连接 */
        DIRECT
    }
    
    /**
     * 判断目标地址应该使用哪种连接方式
     * 
     * @param targetHost 目标主机地址（可能是IP或域名）
     * @param targetPort 目标端口
     * @return 连接方式
     */
    ConnectionType shouldUseProxy(String targetHost, int targetPort);
    
    /**
     * 获取当前过滤模式
     * 
     * @return 过滤模式
     */
    FilterMode getFilterMode();
    
    /**
     * 设置过滤模式
     * 
     * @param mode 过滤模式
     */
    void setFilterMode(FilterMode mode);
}
