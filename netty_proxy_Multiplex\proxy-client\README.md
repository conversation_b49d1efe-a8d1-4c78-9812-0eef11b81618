# Proxy Client - 多协议代理客户端

基于Netty实现的高性能多协议代理客户端，支持SOCKS5和HTTP CONNECT协议，具备智能地址过滤、SSL/TLS加密和GraalVM Native Image支持。

## 🚀 核心特性

### 🌐 多协议支持
- **SOCKS5代理**: 完整的SOCKS5协议实现
- **HTTP CONNECT**: HTTP隧道代理协议
- **多路复用**: 单连接支持多个并发会话
- **协议兼容**: 完全兼容标准代理协议

### 🏗️ 多组件架构
- **统一管理**: 所有协议共享ConnectionManager
- **独立端口**: 每个协议使用独立监听端口
- **资源共享**: 共享地址过滤器和认证机制
- **灵活配置**: 支持配置文件和命令行参数

### 🌍 智能地址过滤
- **三种模式**: ALL_PROXY、CHINA_DIRECT、ALL_DIRECT
- **GeoIP判断**: 基于APNIC官方数据的高精度IP判断
- **在线更新**: 支持从GitHub等在线源获取最新数据
- **智能分流**: 中国IP直连，海外IP代理

### 🔒 安全特性
- **SSL/TLS加密**: 完整的客户端SSL/TLS支持
- **Basic认证**: 支持用户名密码认证
- **证书管理**: 支持自签名和CA证书
- **双向认证**: 支持客户端证书认证

### ⚡ 高性能
- **GraalVM Native Image**: 支持原生编译
- **极速启动**: 启动时间 < 100ms
- **低内存占用**: 运行时内存 < 50MB
- **单文件部署**: 无需Java环境

## 📁 项目结构

```
proxy-client/
├── pom.xml                          # Maven配置文件
├── README.md                        # 本文档
├── README-ADDRESS-FILTER.md         # 地址过滤功能说明
├── README-GRAALVM.md                # GraalVM构建指南
├── build-native.bat                 # Windows原生构建脚本
├── build-native.sh                  # Linux/macOS原生构建脚本
└── src/main/java/com/proxy/client/
    ├── ProxyClient.java             # 多组件客户端主类
    ├── ProxyClientManager.java      # 客户端管理器
    ├── config/                      # 配置管理模块
    │   ├── ProxyClientConfigManager.java
    │   ├── properties/              # 配置属性类
    │   ├── annotation/              # 配置注解
    │   └── binder/                  # 配置绑定器
    ├── inbound/                     # 多协议接入模块
    │   ├── ProxyInbound.java        # 接入组件接口
    │   ├── AbstractProxyInbound.java
    │   └── impl/                    # 协议实现
    │       ├── Socks5Inbound.java   # SOCKS5接入组件
    │       └── HttpInbound.java     # HTTP接入组件
    ├── connection/                  # 连接管理模块
    │   ├── ConnectionManager.java
    │   ├── DirectConnectionHandler.java
    │   └── SessionHandler.java
    ├── filter/                      # 地址过滤模块
    │   ├── AddressFilter.java
    │   └── DefaultAddressFilter.java
    ├── handler/                     # 协议处理器
    │   ├── MultiplexSocks5Handler.java
    │   └── MultiplexSessionHandler.java
    ├── ssl/                         # SSL/TLS支持
    │   └── ClientSslContextManager.java
    ├── protocol/                    # 多路复用协议
    │   └── MultiplexProtocol.java
    ├── util/                        # 工具类
    │   └── GeoIPUtil.java           # IP地理位置判断
    └── resources/
        ├── proxy-client.properties  # 配置文件
        └── china-ip-ranges.txt      # 中国IP段数据
```

## 🚀 快速开始

### 1. 编译项目

```bash
# 标准编译
mvn clean compile

# 打包为可执行JAR
mvn clean package
```

### 2. GraalVM Native Image构建（推荐）

#### Windows:
```cmd
build-native.bat
```

#### Linux/macOS:
```bash
chmod +x build-native.sh
./build-native.sh
```

### 3. 启动方式

#### 方式1：配置文件启动（推荐）
```bash
# JAR方式
java -jar target/proxy-client-1.0.0.jar

# Native Image方式
./target/proxy-client
```

#### 方式2：命令行参数启动
```bash
# 启动SOCKS5代理（端口1080）
java -jar target/proxy-client-1.0.0.jar 1080

# 启动指定协议和端口
java -jar target/proxy-client-1.0.0.jar socks5 1080
java -jar target/proxy-client-1.0.0.jar http 8080

# 同时启动SOCKS5和HTTP
java -jar target/proxy-client-1.0.0.jar both 1080

# 多组件模式（SOCKS5端口 HTTP端口 代理服务器地址 代理服务器端口）
java -jar target/proxy-client-1.0.0.jar 1080 8080 localhost 8888
```

### 4. 浏览器配置

#### SOCKS5代理设置
```
代理服务器: localhost
端口: 1080 (或配置的端口)
协议: SOCKS5
```

#### HTTP代理设置
```
HTTP代理: localhost:8080
HTTPS代理: localhost:8080
```

## ⚙️ 配置说明

### YAML配置文件示例

```yaml
# 代理服务器配置
proxy:
  server:
    host: "localhost"
    port: 8888

# 地址过滤配置
filter:
  mode: "CHINA_DIRECT"  # ALL_PROXY, CHINA_DIRECT, ALL_DIRECT

# 接入器配置
inbound:
  socks5:
    - name: "socks5-main"
      port: 1081
      enabled: true
      description: "主要SOCKS5代理"
  http:
    - name: "http-main"
      port: 1082
      enabled: true
      description: "主要HTTP代理"

# 认证配置
auth:
  enable: false
  username: "admin"
  password: "password"

# SSL配置
ssl:
  enable: false
  trust-all: false
  trust-store-path: "truststore.p12"
  trust-store-password: "password"
  verify-hostname: true
```

### Properties配置文件示例

```properties
# 地址过滤模式
filter.mode=CHINA_DIRECT

# 代理服务器配置
proxy.server.host=localhost
proxy.server.port=8888

# 本地监听端口
local.port=1080

# 认证配置
auth.enable=false
auth.username=admin
auth.password=password
```

## 🔧 高级功能

### 地址过滤模式

- **ALL_PROXY**: 所有连接都通过代理服务器转发
- **CHINA_DIRECT**: 中国IP直连，海外IP通过代理
- **ALL_DIRECT**: 所有连接都直连

### SSL/TLS配置

支持多种SSL配置模式：

- **开发测试模式**: 信任所有证书，禁用主机名验证
- **生产环境模式**: 严格证书验证，启用主机名验证
- **双向认证**: 支持客户端证书认证

### 多组件配置

可以同时启动多个相同或不同协议的接入组件：

```yaml
inbound:
  socks5:
    - name: "socks5-main"
      port: 1081
      enabled: true
    - name: "socks5-backup"
      port: 1083
      enabled: false
  http:
    - name: "http-main"
      port: 1082
      enabled: true
    - name: "http-backup"
      port: 1085
      enabled: false
```

## 📚 相关文档

- [地址过滤功能说明](README-ADDRESS-FILTER.md)
- [GraalVM Native Image构建指南](README-GRAALVM.md)
- [多组件代理接入使用指南](../MULTI_INBOUND_GUIDE.md)
- [SSL配置参考](../SSL_CONFIGURATION_REFERENCE.md)
- [配置文件指南](../CONFIGURATION_GUIDE.md)

## 🛠️ 技术栈

- **Java 17**: 现代Java特性支持
- **Maven 3.x**: 项目构建和依赖管理
- **Netty 4.1.100.Final**: 高性能异步网络框架
- **GraalVM Native Image**: 原生可执行文件编译
- **SnakeYAML**: YAML配置文件解析
- **SLF4J + Logback**: 结构化日志系统

## 🔍 故障排除

### 常见问题

1. **连接失败**: 检查代理服务器是否启动，端口是否正确
2. **认证失败**: 检查用户名密码配置是否正确
3. **SSL握手失败**: 检查SSL证书配置和信任库设置
4. **地址过滤不生效**: 检查过滤模式配置和IP段数据

### 日志级别

- **DEBUG**: 详细的连接和会话信息
- **INFO**: 连接建立和配置加载信息
- **WARN**: 异常情况和性能警告
- **ERROR**: 严重错误和异常堆栈

### 性能调优

- 调整连接池大小和超时时间
- 优化地址过滤缓存配置
- 监控内存使用和连接状态
- 定期更新IP段数据

## 📄 许可证

本项目采用 MIT 许可证。详见 [LICENSE](../LICENSE) 文件。
