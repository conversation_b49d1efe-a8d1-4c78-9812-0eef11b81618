# 恶意内容配置指南

## 🎯 概述

本指南介绍如何通过配置文件管理恶意域名、关键词和白名单，以及如何从网络获取最新的威胁情报数据。

## 📁 配置文件结构

### 配置文件位置
```
proxy-server/src/main/resources/
├── malicious-domains.txt      # 恶意域名黑名单
├── malicious-keywords.txt     # 恶意关键词列表
└── whitelist-domains.txt      # 白名单域名列表
```

### 外部配置文件
也可以在运行目录下放置配置文件，系统会优先加载外部文件：
```
./malicious-domains.txt
./malicious-keywords.txt
./whitelist-domains.txt
```

## 📝 配置文件格式

### 1. 恶意域名配置 (malicious-domains.txt)
```
# 恶意域名黑名单配置文件
# 每行一个域名，支持注释（以#开头的行）

# 已知恶意域名
malware-site.com
phishing-bank.com
fake-paypal.com

# 钓鱼网站
fake-microsoft.com
scam-apple.com
phishing-amazon.com

# 恶意软件分发站点
virus-download.net
trojan-source.org
malware-host.com
```

### 2. 恶意关键词配置 (malicious-keywords.txt)
```
# 恶意关键词配置文件
# 每行一个关键词，支持注释（以#开头的行）
# 关键词会被转换为小写进行匹配

# 成人内容相关
porn
xxx
adult
erotic

# 赌博相关
casino
gambling
bet
poker

# 网络威胁
phishing
scam
fraud
malware
virus
```

### 3. 白名单域名配置 (whitelist-domains.txt)
```
# 白名单域名配置文件
# 每行一个域名，支持注释（以#开头的行）
# 这些域名即使在海外也会被允许访问

# 搜索引擎
google.com
bing.com
yahoo.com

# 社交媒体
youtube.com
facebook.com
twitter.com

# 技术平台
github.com
stackoverflow.com
mozilla.org
```

## 🌐 在线威胁情报更新

### 自动更新功能
系统内置了在线威胁情报更新器，可以从多个可信数据源获取最新的恶意域名：

- **Malware Domain List**: 恶意域名列表
- **URLhaus**: 恶意URL数据库
- **OpenPhish**: 钓鱼网站数据
- **PhishTank**: 钓鱼网站数据库

### 更新频率
- 默认每6小时自动更新一次
- 支持手动触发更新
- 更新失败时会使用本地配置文件

### 数据源配置
可以在 `OnlineThreatIntelligenceUpdater.java` 中配置数据源：

```java
private static final ThreatIntelSource[] THREAT_INTEL_SOURCES = {
    new ThreatIntelSource(
        "Malware Domain List",
        "https://www.malwaredomainlist.com/hostslist/hosts.txt",
        ThreatIntelSource.Type.HOSTS_FILE
    ),
    // 添加更多数据源...
};
```

## 🔧 运行时管理

### 动态重新加载配置
```java
// 重新加载恶意内容配置
GeoLocationFilter.getInstance().reloadMaliciousContent();

// 重新加载白名单配置
GeoLocationFilter.getInstance().reloadWhitelistContent();

// 重新加载所有配置
GeoLocationFilter.getInstance().reloadAllConfigurations();
```

### 动态添加/删除域名
```java
// 添加恶意域名
GeoLocationFilter.getInstance().addMaliciousDomain("new-malicious-site.com");

// 移除恶意域名
GeoLocationFilter.getInstance().removeMaliciousDomain("false-positive.com");

// 添加白名单域名
GeoLocationFilter.getInstance().addWhitelistDomain("trusted-site.com");

// 移除白名单域名
GeoLocationFilter.getInstance().removeWhitelistDomain("no-longer-trusted.com");
```

### 手动触发威胁情报更新
```java
// 手动更新威胁情报
OnlineThreatIntelligenceUpdater.getInstance().forceUpdate();
```

## 📊 配置统计和监控

### 查看配置统计
```java
FilterStats stats = GeoLocationFilter.getInstance().getStats();
System.out.println("总请求数: " + stats.getTotalRequests());
System.out.println("阻止数: " + stats.getTotalBlocked());
System.out.println("允许数: " + stats.getAllowedRequests());
```

### 监控更新状态
```java
OnlineThreatIntelligenceUpdater updater = OnlineThreatIntelligenceUpdater.getInstance();
System.out.println("最后更新时间: " + new Date(updater.getLastUpdateTime()));
System.out.println("是否正在更新: " + updater.isUpdating());
```

## 🛠️ 配置文件维护

### 1. 定期更新配置文件
- 建议每周检查和更新配置文件
- 关注安全厂商发布的威胁情报
- 及时添加新发现的恶意域名

### 2. 验证配置文件格式
- 确保每行只有一个域名或关键词
- 注释行以#开头
- 域名格式正确（包含点，不以点开头或结尾）

### 3. 备份配置文件
- 定期备份配置文件
- 记录配置变更历史
- 测试配置文件的有效性

### 4. 性能优化
- 避免添加过多的关键词（建议<500个）
- 定期清理无效的域名
- 监控内存使用情况

## 🔍 故障排除

### 常见问题

1. **配置文件未生效**
   - 检查文件路径是否正确
   - 确认文件格式是否正确
   - 查看日志中的加载信息

2. **在线更新失败**
   - 检查网络连接
   - 确认数据源URL是否可访问
   - 查看更新日志

3. **误杀合法网站**
   - 将域名添加到白名单
   - 检查关键词是否过于宽泛
   - 调整过滤策略

4. **恶意网站未被拦截**
   - 检查域名是否在黑名单中
   - 确认关键词匹配规则
   - 手动添加到黑名单

### 调试方法

1. **启用详细日志**
   ```xml
   <logger name="com.proxy.server.filter" level="DEBUG"/>
   ```

2. **查看配置加载日志**
   ```
   INFO  - 从配置文件加载恶意内容完成 - 域名: 156, 关键词: 45, 模式: 23
   INFO  - 从配置文件加载白名单域名完成 - 域名: 89, 模式: 8
   ```

3. **监控过滤统计**
   ```
   地理位置过滤统计:
     总阻止数: 1234
     域名过滤: 567
     关键词过滤: 234
     海外可疑: 345
   ```

## 📋 最佳实践

### 1. 配置管理
- 使用版本控制管理配置文件
- 建立配置审核流程
- 定期评估过滤效果

### 2. 安全策略
- 采用白名单优先策略
- 定期更新威胁情报
- 监控误报和漏报

### 3. 性能优化
- 合理设置缓存大小
- 定期清理过期数据
- 监控系统资源使用

### 4. 运维管理
- 建立告警机制
- 定期备份配置
- 记录操作日志

## 🔮 扩展功能

### 1. 自定义数据源
可以添加自己的威胁情报数据源：
```java
new ThreatIntelSource(
    "Custom Source",
    "https://your-domain.com/threat-intel.txt",
    ThreatIntelSource.Type.PLAIN_TEXT
)
```

### 2. API集成
支持与第三方威胁情报API集成：
- VirusTotal API
- URLVoid API
- Hybrid Analysis API

### 3. 机器学习
未来可以集成机器学习模型：
- 域名特征分析
- 恶意URL检测
- 行为模式识别

### 4. 实时更新
支持实时威胁情报推送：
- WebSocket连接
- 消息队列集成
- 事件驱动更新

通过合理配置和维护这些配置文件，可以大大提高地理位置过滤系统的准确性和有效性。
