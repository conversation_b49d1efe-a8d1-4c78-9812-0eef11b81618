package com.proxy.server.core;

import com.proxy.server.protocol.MultiplexProtocolDetector;
import com.proxy.server.ssl.SslContextManager;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.ssl.SslContext;
import io.netty.handler.ssl.SslHandler;
import io.netty.handler.timeout.IdleStateHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLEngine;
import java.util.concurrent.TimeUnit;

/**
 * 代理服务器通道初始化器
 */
public class ProxyServerInitializer extends ChannelInitializer<SocketChannel> {
    private static final Logger logger = LoggerFactory.getLogger(ProxyServerInitializer.class);

    private final SslContextManager sslContextManager;

    public ProxyServerInitializer() {
        this.sslContextManager = SslContextManager.getInstance();
    }

    @Override
    protected void initChannel(SocketChannel ch) throws Exception {
        ChannelPipeline pipeline = ch.pipeline();

        // 如果启用了SSL，添加SSL处理器
        if (sslContextManager.isSslEnabled()) {
            SslContext sslContext = sslContextManager.getServerSslContext();
            if (sslContext != null) {
                SSLEngine sslEngine = sslContext.newEngine(ch.alloc());

                // 配置SSL引擎
                configureSslEngine(sslEngine);

                SslHandler sslHandler = new SslHandler(sslEngine);

                // 设置握手超时
                int handshakeTimeout = sslContextManager.getHandshakeTimeoutSeconds();
                sslHandler.setHandshakeTimeoutMillis(handshakeTimeout * 1000L);

                pipeline.addLast("ssl", sslHandler);
                logger.debug("为客户端连接添加SSL处理器: {}", ch.remoteAddress());
            }
        }

        // 添加空闲状态处理器（用于连接超时检测）
        pipeline.addLast("idleStateHandler", new IdleStateHandler(0, 0, 300, TimeUnit.SECONDS));

        // 添加协议检测器
        pipeline.addLast("protocolDetector", new MultiplexProtocolDetector());

        logger.debug("客户端连接已建立: {} (SSL: {})",
            ch.remoteAddress(), sslContextManager.isSslEnabled() ? "enabled" : "disabled");
    }

    /**
     * 配置SSL引擎
     */
    private void configureSslEngine(SSLEngine sslEngine) {
        // 设置为服务器模式
        sslEngine.setUseClientMode(false);

        // 根据配置设置客户端认证
        // 这些配置会在SslContextManager中处理，这里主要是确保引擎配置正确
        logger.debug("SSL引擎配置完成，协议: {}, 密码套件数量: {}",
            sslEngine.getEnabledProtocols().length > 0 ? sslEngine.getEnabledProtocols()[0] : "default",
            sslEngine.getEnabledCipherSuites().length);
    }
}