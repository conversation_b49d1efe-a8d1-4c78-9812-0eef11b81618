package com.proxy.server.metrics;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * 性能指标收集器
 * 优化：使用无锁数据结构提升并发性能
 */
public class PerformanceMetrics {
    private static final Logger logger = LoggerFactory.getLogger(PerformanceMetrics.class);
    private static final PerformanceMetrics INSTANCE = new PerformanceMetrics();
    
    // 使用LongAdder替代AtomicLong，在高并发场景下性能更好
    private final LongAdder totalConnections = new LongAdder();
    private final LongAdder totalSessions = new LongAdder();
    private final LongAdder poolHits = new LongAdder();
    private final LongAdder poolMisses = new LongAdder();
    private final LongAdder blacklistHits = new LongAdder();
    private final LongAdder bytesTransferred = new LongAdder();

    // 地理位置过滤统计
    private final LongAdder geoLocationBlocks = new LongAdder();
    private final LongAdder domainFilterBlocks = new LongAdder();
    private final LongAdder keywordFilterBlocks = new LongAdder();
    private final LongAdder overseasSuspiciousBlocks = new LongAdder();
    
    // 使用AtomicLong记录时间戳
    private final AtomicLong startTime = new AtomicLong(System.currentTimeMillis());
    private final AtomicLong lastResetTime = new AtomicLong(System.currentTimeMillis());
    
    // 当前活跃连接数
    private final LongAdder activeConnections = new LongAdder();
    private final LongAdder activeSessions = new LongAdder();
    
    // 响应时间统计（简化版本，避免复杂的同步）
    private final LongAdder totalResponseTime = new LongAdder();
    private final LongAdder responseCount = new LongAdder();
    
    // 错误统计
    private final LongAdder connectionErrors = new LongAdder();
    private final LongAdder timeoutErrors = new LongAdder();
    
    private PerformanceMetrics() {}
    
    public static PerformanceMetrics getInstance() {
        return INSTANCE;
    }
    
    // 连接相关指标
    public void incrementTotalConnections() {
        totalConnections.increment();
    }
    
    public void incrementActiveConnections() {
        activeConnections.increment();
    }
    
    public void decrementActiveConnections() {
        activeConnections.decrement();
    }
    
    // 会话相关指标
    public void incrementTotalSessions() {
        totalSessions.increment();
    }
    
    public void incrementActiveSessions() {
        activeSessions.increment();
    }
    
    public void decrementActiveSessions() {
        activeSessions.decrement();
    }
    
    // 连接池相关指标
    public void incrementPoolHits() {
        poolHits.increment();
    }
    
    public void incrementPoolMisses() {
        poolMisses.increment();
    }
    
    // 黑名单相关指标
    public void incrementBlacklistHits() {
        blacklistHits.increment();
    }

    // 地理位置过滤相关指标
    public void incrementGeoLocationBlocks() {
        geoLocationBlocks.increment();
    }

    public void incrementDomainFilterBlocks() {
        domainFilterBlocks.increment();
    }

    public void incrementKeywordFilterBlocks() {
        keywordFilterBlocks.increment();
    }

    public void incrementOverseasSuspiciousBlocks() {
        overseasSuspiciousBlocks.increment();
    }
    
    // 数据传输指标
    public void addBytesTransferred(long bytes) {
        bytesTransferred.add(bytes);
    }
    
    // 响应时间指标
    public void recordResponseTime(long responseTimeMs) {
        totalResponseTime.add(responseTimeMs);
        responseCount.increment();
    }
    
    // 错误指标
    public void incrementConnectionErrors() {
        connectionErrors.increment();
    }
    
    public void incrementTimeoutErrors() {
        timeoutErrors.increment();
    }
    
    // 获取统计信息
    public MetricsSnapshot getSnapshot() {
        long now = System.currentTimeMillis();
        long uptime = now - startTime.get();
        long timeSinceReset = now - lastResetTime.get();
        
        return new MetricsSnapshot(
            totalConnections.sum(),
            activeConnections.sum(),
            totalSessions.sum(),
            activeSessions.sum(),
            poolHits.sum(),
            poolMisses.sum(),
            blacklistHits.sum(),
            bytesTransferred.sum(),
            connectionErrors.sum(),
            timeoutErrors.sum(),
            getAverageResponseTime(),
            uptime,
            timeSinceReset,
            geoLocationBlocks.sum(),
            domainFilterBlocks.sum(),
            keywordFilterBlocks.sum(),
            overseasSuspiciousBlocks.sum()
        );
    }
    
    private double getAverageResponseTime() {
        long count = responseCount.sum();
        if (count == 0) return 0.0;
        return (double) totalResponseTime.sum() / count;
    }
    
    // 重置统计信息（保留累计值）
    public void resetCounters() {
        lastResetTime.set(System.currentTimeMillis());
        // 注意：不重置累计值，只重置时间戳
        logger.info("性能指标计数器已重置");
    }
    
    // 打印统计信息
    public void logMetrics() {
        MetricsSnapshot snapshot = getSnapshot();
        logger.info("=== 性能指标统计 ===");
        logger.info("运行时间: {}ms", snapshot.uptime);
        logger.info("总连接数: {}, 活跃连接数: {}", snapshot.totalConnections, snapshot.activeConnections);
        logger.info("总会话数: {}, 活跃会话数: {}", snapshot.totalSessions, snapshot.activeSessions);
        logger.info("连接池命中: {}, 未命中: {}, 命中率: {:.2f}%",
            snapshot.poolHits, snapshot.poolMisses, snapshot.getPoolHitRate());
        logger.info("黑名单命中: {}", snapshot.blacklistHits);
        logger.info("传输字节数: {} bytes ({:.2f} MB)",
            snapshot.bytesTransferred, snapshot.bytesTransferred / 1024.0 / 1024.0);
        logger.info("平均响应时间: {:.2f}ms", snapshot.averageResponseTime);
        logger.info("连接错误: {}, 超时错误: {}", snapshot.connectionErrors, snapshot.timeoutErrors);

        // 地理位置过滤统计
        long totalGeoBlocks = snapshot.geoLocationBlocks + snapshot.domainFilterBlocks +
                             snapshot.keywordFilterBlocks + snapshot.overseasSuspiciousBlocks;
        if (totalGeoBlocks > 0) {
            logger.info("地理位置过滤统计:");
            logger.info("  总阻止数: {}", totalGeoBlocks);
            logger.info("  域名过滤: {}", snapshot.domainFilterBlocks);
            logger.info("  关键词过滤: {}", snapshot.keywordFilterBlocks);
            logger.info("  海外可疑: {}", snapshot.overseasSuspiciousBlocks);
            logger.info("  其他地理位置: {}", snapshot.geoLocationBlocks);
        }

        // 添加黑名单详细统计
        try {
            com.proxy.server.blacklist.HostBlacklist blacklist =
                com.proxy.server.blacklist.HostBlacklist.getInstance();
            com.proxy.server.blacklist.HostBlacklist.BlacklistStats blacklistStats = blacklist.getStats();
            if (blacklistStats.totalBlacklistedHosts > 0) {
                logger.info("黑名单详情: {}", blacklistStats);
            }
        } catch (Exception e) {
            logger.debug("获取黑名单统计失败: {}", e.getMessage());
        }

        logger.info("==================");
    }
    
    /**
     * 指标快照类
     */
    public static class MetricsSnapshot {
        public final long totalConnections;
        public final long activeConnections;
        public final long totalSessions;
        public final long activeSessions;
        public final long poolHits;
        public final long poolMisses;
        public final long blacklistHits;
        public final long bytesTransferred;
        public final long connectionErrors;
        public final long timeoutErrors;
        public final double averageResponseTime;
        public final long uptime;
        public final long timeSinceReset;

        // 地理位置过滤统计
        public final long geoLocationBlocks;
        public final long domainFilterBlocks;
        public final long keywordFilterBlocks;
        public final long overseasSuspiciousBlocks;
        
        public MetricsSnapshot(long totalConnections, long activeConnections,
                             long totalSessions, long activeSessions,
                             long poolHits, long poolMisses, long blacklistHits,
                             long bytesTransferred, long connectionErrors,
                             long timeoutErrors, double averageResponseTime,
                             long uptime, long timeSinceReset,
                             long geoLocationBlocks, long domainFilterBlocks,
                             long keywordFilterBlocks, long overseasSuspiciousBlocks) {
            this.totalConnections = totalConnections;
            this.activeConnections = activeConnections;
            this.totalSessions = totalSessions;
            this.activeSessions = activeSessions;
            this.poolHits = poolHits;
            this.poolMisses = poolMisses;
            this.blacklistHits = blacklistHits;
            this.bytesTransferred = bytesTransferred;
            this.connectionErrors = connectionErrors;
            this.timeoutErrors = timeoutErrors;
            this.averageResponseTime = averageResponseTime;
            this.uptime = uptime;
            this.timeSinceReset = timeSinceReset;

            // 地理位置过滤统计
            this.geoLocationBlocks = geoLocationBlocks;
            this.domainFilterBlocks = domainFilterBlocks;
            this.keywordFilterBlocks = keywordFilterBlocks;
            this.overseasSuspiciousBlocks = overseasSuspiciousBlocks;
        }
        
        public double getPoolHitRate() {
            long total = poolHits + poolMisses;
            return total == 0 ? 0.0 : (double) poolHits / total * 100.0;
        }
    }
}
