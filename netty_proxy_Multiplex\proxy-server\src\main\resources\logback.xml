<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    
    <logger name="io.netty" level="INFO"/>
    <!--<logger name="com.proxy.server" level="DEBUG"/>-->
    
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
    </root>
</configuration>