package com.proxy.server.config.properties;

import com.proxy.server.config.annotation.ConfigurationProperties;

/**
 * 代理服务器配置属性
 * 使用 YAML 配置绑定
 */
@ConfigurationProperties
public class ProxyServerProperties {
    
    private ServerProperties server = new ServerProperties();
    private AuthProperties auth = new AuthProperties();
    private PoolProperties pool = new PoolProperties();
    private MetricsProperties metrics = new MetricsProperties();
    private BlacklistProperties blacklist = new BlacklistProperties();
    private SslProperties ssl = new SslProperties();
    private GeoLocationFilterProperties geoLocationFilter = new GeoLocationFilterProperties();
    private PerformanceProperties performance = new PerformanceProperties();
    
    public ServerProperties getServer() {
        return server;
    }
    
    public void setServer(ServerProperties server) {
        this.server = server;
    }
    
    public AuthProperties getAuth() {
        return auth;
    }
    
    public void setAuth(AuthProperties auth) {
        this.auth = auth;
    }
    
    public PoolProperties getPool() {
        return pool;
    }
    
    public void setPool(PoolProperties pool) {
        this.pool = pool;
    }
    
    public MetricsProperties getMetrics() {
        return metrics;
    }
    
    public void setMetrics(MetricsProperties metrics) {
        this.metrics = metrics;
    }
    
    public BlacklistProperties getBlacklist() {
        return blacklist;
    }
    
    public void setBlacklist(BlacklistProperties blacklist) {
        this.blacklist = blacklist;
    }

    public SslProperties getSsl() {
        return ssl;
    }

    public void setSsl(SslProperties ssl) {
        this.ssl = ssl;
    }

    public GeoLocationFilterProperties getGeoLocationFilter() {
        return geoLocationFilter;
    }

    public void setGeoLocationFilter(GeoLocationFilterProperties geoLocationFilter) {
        this.geoLocationFilter = geoLocationFilter;
    }

    public PerformanceProperties getPerformance() {
        return performance;
    }

    public void setPerformance(PerformanceProperties performance) {
        this.performance = performance;
    }

    /**
     * 服务器配置
     */
    public static class ServerProperties {
        private int port = 8888;
        
        public int getPort() {
            return port;
        }
        
        public void setPort(int port) {
            this.port = port;
        }
    }
    
    /**
     * 认证配置
     */
    public static class AuthProperties {
        private boolean enable = false;
        private String username = "admin";
        private String password = "password";
        private TimeoutProperties timeout = new TimeoutProperties();
        
        public boolean isEnable() {
            return enable;
        }
        
        public void setEnable(boolean enable) {
            this.enable = enable;
        }
        
        public String getUsername() {
            return username;
        }
        
        public void setUsername(String username) {
            this.username = username;
        }
        
        public String getPassword() {
            return password;
        }
        
        public void setPassword(String password) {
            this.password = password;
        }
        
        public TimeoutProperties getTimeout() {
            return timeout;
        }
        
        public void setTimeout(TimeoutProperties timeout) {
            this.timeout = timeout;
        }
        
        public static class TimeoutProperties {
            private int seconds = 30;
            
            public int getSeconds() {
                return seconds;
            }
            
            public void setSeconds(int seconds) {
                this.seconds = seconds;
            }
        }
    }
    
    /**
     * 连接池配置
     */
    public static class PoolProperties {
        private boolean enable = true;
        private MaxConnectionsProperties maxConnections = new MaxConnectionsProperties();
        private IdleTimeoutProperties idleTimeout = new IdleTimeoutProperties();
        private CleanupIntervalProperties cleanupInterval = new CleanupIntervalProperties();
        
        public boolean isEnable() {
            return enable;
        }
        
        public void setEnable(boolean enable) {
            this.enable = enable;
        }
        
        public MaxConnectionsProperties getMaxConnections() {
            return maxConnections;
        }
        
        public void setMaxConnections(MaxConnectionsProperties maxConnections) {
            this.maxConnections = maxConnections;
        }
        
        public IdleTimeoutProperties getIdleTimeout() {
            return idleTimeout;
        }
        
        public void setIdleTimeout(IdleTimeoutProperties idleTimeout) {
            this.idleTimeout = idleTimeout;
        }
        
        public CleanupIntervalProperties getCleanupInterval() {
            return cleanupInterval;
        }
        
        public void setCleanupInterval(CleanupIntervalProperties cleanupInterval) {
            this.cleanupInterval = cleanupInterval;
        }
        
        public static class MaxConnectionsProperties {
            private int perHost = 20;
            
            public int getPerHost() {
                return perHost;
            }
            
            public void setPerHost(int perHost) {
                this.perHost = perHost;
            }
        }
        
        public static class IdleTimeoutProperties {
            private int seconds = 60;
            
            public int getSeconds() {
                return seconds;
            }
            
            public void setSeconds(int seconds) {
                this.seconds = seconds;
            }
        }
        
        public static class CleanupIntervalProperties {
            private int seconds = 30;
            
            public int getSeconds() {
                return seconds;
            }
            
            public void setSeconds(int seconds) {
                this.seconds = seconds;
            }
        }
    }
    
    /**
     * 性能监控配置
     */
    public static class MetricsProperties {
        private boolean enable = true;
        private ReportProperties report = new ReportProperties();
        
        public boolean isEnable() {
            return enable;
        }
        
        public void setEnable(boolean enable) {
            this.enable = enable;
        }
        
        public ReportProperties getReport() {
            return report;
        }
        
        public void setReport(ReportProperties report) {
            this.report = report;
        }
        
        public static class ReportProperties {
            private IntervalProperties interval = new IntervalProperties();
            
            public IntervalProperties getInterval() {
                return interval;
            }
            
            public void setInterval(IntervalProperties interval) {
                this.interval = interval;
            }
            
            public static class IntervalProperties {
                private int seconds = 60;
                
                public int getSeconds() {
                    return seconds;
                }
                
                public void setSeconds(int seconds) {
                    this.seconds = seconds;
                }
            }
        }
    }
    
    /**
     * 黑名单配置
     */
    public static class BlacklistProperties {
        private boolean enable = true;
        private FailureProperties failure = new FailureProperties();
        private CacheProperties cache = new CacheProperties();
        
        public boolean isEnable() {
            return enable;
        }
        
        public void setEnable(boolean enable) {
            this.enable = enable;
        }
        
        public FailureProperties getFailure() {
            return failure;
        }
        
        public void setFailure(FailureProperties failure) {
            this.failure = failure;
        }
        
        public CacheProperties getCache() {
            return cache;
        }
        
        public void setCache(CacheProperties cache) {
            this.cache = cache;
        }
        
        public static class FailureProperties {
            private int threshold = 3;
            
            public int getThreshold() {
                return threshold;
            }
            
            public void setThreshold(int threshold) {
                this.threshold = threshold;
            }
        }
        
        public static class CacheProperties {
            private TimeoutProperties timeout = new TimeoutProperties();
            
            public TimeoutProperties getTimeout() {
                return timeout;
            }
            
            public void setTimeout(TimeoutProperties timeout) {
                this.timeout = timeout;
            }
            
            public static class TimeoutProperties {
                private int seconds = 300;
                
                public int getSeconds() {
                    return seconds;
                }
                
                public void setSeconds(int seconds) {
                    this.seconds = seconds;
                }
            }
        }
    }

    /**
     * SSL/TLS配置
     */
    public static class SslProperties {
        private boolean enable = false;
        private String keyStorePath = "server.p12";
        private String keyStorePassword = "changeit";
        private String keyStoreType = "PKCS12";
        private String trustStorePath = "";
        private String trustStorePassword = "changeit";
        private String trustStoreType = "PKCS12";
        private String[] protocols = {"TLSv1.2", "TLSv1.3"};
        private String[] cipherSuites = {};
        private boolean clientAuth = false;
        private boolean needClientAuth = false;
        private boolean wantClientAuth = false;
        private int handshakeTimeoutSeconds = 30;

        public boolean isEnable() {
            return enable;
        }

        public void setEnable(boolean enable) {
            this.enable = enable;
        }

        public String getKeyStorePath() {
            return keyStorePath;
        }

        public void setKeyStorePath(String keyStorePath) {
            this.keyStorePath = keyStorePath;
        }

        public String getKeyStorePassword() {
            return keyStorePassword;
        }

        public void setKeyStorePassword(String keyStorePassword) {
            this.keyStorePassword = keyStorePassword;
        }

        public String getKeyStoreType() {
            return keyStoreType;
        }

        public void setKeyStoreType(String keyStoreType) {
            this.keyStoreType = keyStoreType;
        }

        public String getTrustStorePath() {
            return trustStorePath;
        }

        public void setTrustStorePath(String trustStorePath) {
            this.trustStorePath = trustStorePath;
        }

        public String getTrustStorePassword() {
            return trustStorePassword;
        }

        public void setTrustStorePassword(String trustStorePassword) {
            this.trustStorePassword = trustStorePassword;
        }

        public String getTrustStoreType() {
            return trustStoreType;
        }

        public void setTrustStoreType(String trustStoreType) {
            this.trustStoreType = trustStoreType;
        }

        public String[] getProtocols() {
            return protocols;
        }

        public void setProtocols(String[] protocols) {
            this.protocols = protocols;
        }

        public String[] getCipherSuites() {
            return cipherSuites;
        }

        public void setCipherSuites(String[] cipherSuites) {
            this.cipherSuites = cipherSuites;
        }

        public boolean isClientAuth() {
            return clientAuth;
        }

        public void setClientAuth(boolean clientAuth) {
            this.clientAuth = clientAuth;
        }

        public boolean isNeedClientAuth() {
            return needClientAuth;
        }

        public void setNeedClientAuth(boolean needClientAuth) {
            this.needClientAuth = needClientAuth;
        }

        public boolean isWantClientAuth() {
            return wantClientAuth;
        }

        public void setWantClientAuth(boolean wantClientAuth) {
            this.wantClientAuth = wantClientAuth;
        }

        public int getHandshakeTimeoutSeconds() {
            return handshakeTimeoutSeconds;
        }

        public void setHandshakeTimeoutSeconds(int handshakeTimeoutSeconds) {
            this.handshakeTimeoutSeconds = handshakeTimeoutSeconds;
        }
    }

    /**
     * 地理位置过滤配置
     */
    public static class GeoLocationFilterProperties {
        private boolean enable = true;
        private boolean blockOverseasSuspicious = true;
        private boolean enableDomainFilter = true;
        private boolean enableKeywordFilter = true;
        private boolean enableWhitelist = true;
        private int dnsCacheTimeoutMinutes = 5;
        private int ipCacheTimeoutMinutes = 60;
        private int maxCacheSize = 10000;
        private boolean autoUpdateIPRanges = true;
        private int updateIntervalHours = 24;
        private OnlineDataSourcesProperties onlineDataSources = new OnlineDataSourcesProperties();

        public boolean isEnable() {
            return enable;
        }

        public void setEnable(boolean enable) {
            this.enable = enable;
        }

        public boolean isBlockOverseasSuspicious() {
            return blockOverseasSuspicious;
        }

        public void setBlockOverseasSuspicious(boolean blockOverseasSuspicious) {
            this.blockOverseasSuspicious = blockOverseasSuspicious;
        }

        public boolean isEnableDomainFilter() {
            return enableDomainFilter;
        }

        public void setEnableDomainFilter(boolean enableDomainFilter) {
            this.enableDomainFilter = enableDomainFilter;
        }

        public boolean isEnableKeywordFilter() {
            return enableKeywordFilter;
        }

        public void setEnableKeywordFilter(boolean enableKeywordFilter) {
            this.enableKeywordFilter = enableKeywordFilter;
        }

        public boolean isEnableWhitelist() {
            return enableWhitelist;
        }

        public void setEnableWhitelist(boolean enableWhitelist) {
            this.enableWhitelist = enableWhitelist;
        }

        public int getDnsCacheTimeoutMinutes() {
            return dnsCacheTimeoutMinutes;
        }

        public void setDnsCacheTimeoutMinutes(int dnsCacheTimeoutMinutes) {
            this.dnsCacheTimeoutMinutes = dnsCacheTimeoutMinutes;
        }

        public int getIpCacheTimeoutMinutes() {
            return ipCacheTimeoutMinutes;
        }

        public void setIpCacheTimeoutMinutes(int ipCacheTimeoutMinutes) {
            this.ipCacheTimeoutMinutes = ipCacheTimeoutMinutes;
        }

        public int getMaxCacheSize() {
            return maxCacheSize;
        }

        public void setMaxCacheSize(int maxCacheSize) {
            this.maxCacheSize = maxCacheSize;
        }

        public boolean isAutoUpdateIPRanges() {
            return autoUpdateIPRanges;
        }

        public void setAutoUpdateIPRanges(boolean autoUpdateIPRanges) {
            this.autoUpdateIPRanges = autoUpdateIPRanges;
        }

        public int getUpdateIntervalHours() {
            return updateIntervalHours;
        }

        public void setUpdateIntervalHours(int updateIntervalHours) {
            this.updateIntervalHours = updateIntervalHours;
        }

        public OnlineDataSourcesProperties getOnlineDataSources() {
            return onlineDataSources;
        }

        public void setOnlineDataSources(OnlineDataSourcesProperties onlineDataSources) {
            this.onlineDataSources = onlineDataSources;
        }
    }

    /**
     * 在线数据源配置
     */
    public static class OnlineDataSourcesProperties {
        private java.util.List<String> maliciousDomains = java.util.Arrays.asList(
            "https://raw.githubusercontent.com/StevenBlack/hosts/master/hosts",
            "https://someonewhocares.org/hosts/zero/hosts",
            "https://raw.githubusercontent.com/AdguardTeam/AdguardFilters/master/BaseFilter/sections/adservers.txt"
        );

        private java.util.List<String> maliciousKeywords = java.util.Arrays.asList(
            "https://raw.githubusercontent.com/crazy-max/WindowsSpyBlocker/master/data/hosts/spy.txt"
        );

        private java.util.List<String> chinaIpRanges = java.util.Arrays.asList(
            "https://raw.githubusercontent.com/mayaxcn/china-ip-list/master/chnroute.txt",
            "https://raw.githubusercontent.com/17mon/china_ip_list/master/china_ip_list.txt"
        );

        public java.util.List<String> getMaliciousDomains() {
            return maliciousDomains;
        }

        public void setMaliciousDomains(java.util.List<String> maliciousDomains) {
            this.maliciousDomains = maliciousDomains;
        }

        public java.util.List<String> getMaliciousKeywords() {
            return maliciousKeywords;
        }

        public void setMaliciousKeywords(java.util.List<String> maliciousKeywords) {
            this.maliciousKeywords = maliciousKeywords;
        }

        public java.util.List<String> getChinaIpRanges() {
            return chinaIpRanges;
        }

        public void setChinaIpRanges(java.util.List<String> chinaIpRanges) {
            this.chinaIpRanges = chinaIpRanges;
        }
    }

    /**
     * 性能配置
     */
    public static class PerformanceProperties {
        private int bossThreads = 0; // 0表示默认值1
        private int workerThreads = 0; // 0表示自动计算

        public int getBossThreads() {
            return bossThreads;
        }

        public void setBossThreads(int bossThreads) {
            this.bossThreads = bossThreads;
        }

        public int getWorkerThreads() {
            return workerThreads;
        }

        public void setWorkerThreads(int workerThreads) {
            this.workerThreads = workerThreads;
        }
    }
}
