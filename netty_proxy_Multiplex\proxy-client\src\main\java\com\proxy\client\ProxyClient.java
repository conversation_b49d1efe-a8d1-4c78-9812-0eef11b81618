package com.proxy.client;

import com.proxy.client.config.properties.ProxyClientProperties;
import com.proxy.client.config.ProxyClientConfigManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 多组件代理客户端主启动类
 * 支持同时启动HTTP和SOCKS5代理接入
 */
public class ProxyClient {
    private static final Logger logger = LoggerFactory.getLogger(ProxyClient.class);
    
    public static void main(String[] args) {
        logger.info("启动多组件代理客户端...");

        try {
            // 解析配置文件相关的命令行参数
            parseConfigArgs(args);

            // 启动时初始化所有配置文件和组件
            initializeComponents();

            // 加载配置
            ProxyClientConfigManager configManager = ProxyClientConfigManager.getInstance();

            // 创建代理客户端管理器
            ProxyClientManager manager = new ProxyClientManager();

            // 根据配置添加组件
            addInboundsFromConfig(manager, configManager, args);

            // 启动所有组件
            manager.start().join();

            // 输出启动信息
            logger.info("多组件代理客户端启动完成");
            manager.printStatus();

            // 等待停止信号
            manager.awaitTermination();

        } catch (Exception e) {
            logger.error("启动多组件代理客户端失败: {}", e.getMessage(), e);
            System.exit(1);
        }
    }

    /**
     * 启动时初始化所有组件和配置文件
     */
    private static void initializeComponents() {
        logger.info("开始初始化客户端组件和配置文件...");

        try {
            // 1. 初始化 GeoIPUtil（触发IP段数据加载和在线更新）
            logger.info("初始化地理位置IP判断工具...");
            // 通过调用静态方法触发静态初始化块
            int loadedCount = com.proxy.client.util.GeoIPUtil.getLoadedIPRangeCount();
            logger.info("已加载 {} 个IP段", loadedCount);

            logger.info("客户端组件和配置文件初始化完成");

        } catch (Exception e) {
            logger.error("初始化客户端组件失败", e);
            // 不要因为初始化失败就退出，让客户端继续启动
            logger.warn("将使用默认配置继续启动客户端");
        }
    }

    /**
     * 根据配置添加接入组件
     */
    private static void addInboundsFromConfig(ProxyClientManager manager, ProxyClientConfigManager configManager, String[] args) {
        // 命令行参数解析
        if (args.length > 0) {
            parseCommandLineArgs(manager, configManager, args);
            return;
        }

        // 从新的配置格式添加组件
        addInboundsFromNewConfig(manager, configManager);

        // 如果没有启用任何组件，尝试从旧配置添加
        if (manager.getInbounds().isEmpty()) {
            addInboundsFromLegacyConfig(manager, configManager);
        }

        // 如果仍然没有任何组件，使用默认配置
        if (manager.getInbounds().isEmpty()) {
            logger.warn("没有启用任何接入组件，默认启用SOCKS5");
            manager.addSocks5Inbound(configManager.getLocalPort());
        }
    }

    /**
     * 从新的配置格式添加接入组件
     */
    private static void addInboundsFromNewConfig(ProxyClientManager manager, ProxyClientConfigManager configManager) {
        // 添加启用的 SOCKS5 接入器
        for (ProxyClientProperties.InboundItemProperties inboundConfig : configManager.getEnabledSocks5Inbounds()) {
            manager.addSocks5Inbound(inboundConfig.getPort());
            logger.info("添加SOCKS5接入组件: {} (端口 {}) - {}",
                       inboundConfig.getName(), inboundConfig.getPort(), inboundConfig.getDescription());
        }

        // 添加启用的 HTTP 接入器
        for (ProxyClientProperties.InboundItemProperties inboundConfig : configManager.getEnabledHttpInbounds()) {
            manager.addHttpInbound(inboundConfig.getPort());
            logger.info("添加HTTP接入组件: {} (端口 {}) - {}",
                       inboundConfig.getName(), inboundConfig.getPort(), inboundConfig.getDescription());
        }
    }

    /**
     * 从旧配置格式添加接入组件（向后兼容）
     */
    private static void addInboundsFromLegacyConfig(ProxyClientManager manager, ProxyClientConfigManager configManager) {
        String[] enabledInbounds = configManager.getEnabledInbounds();

        for (String inboundType : enabledInbounds) {
            switch (inboundType.toLowerCase()) {
                case "socks5":
                    if (configManager.isSocks5Enabled()) {
                        manager.addSocks5Inbound(configManager.getSocks5Port());
                        logger.info("添加SOCKS5接入组件 (旧配置): 端口 {}", configManager.getSocks5Port());
                    }
                    break;

                case "http":
                    if (configManager.isHttpEnabled()) {
                        manager.addHttpInbound(configManager.getHttpPort());
                        logger.info("添加HTTP接入组件 (旧配置): 端口 {}", configManager.getHttpPort());
                    }
                    break;

                default:
                    logger.warn("未知的接入组件类型: {}", inboundType);
            }
        }
    }
    
    /**
     * 解析命令行参数
     */
    private static void parseCommandLineArgs(ProxyClientManager manager, ProxyClientConfigManager configManager, String[] args) {
        if (args.length == 1) {
            // 单参数：端口号，启动SOCKS5
            try {
                int port = Integer.parseInt(args[0]);
                manager.addSocks5Inbound(port);
                logger.info("命令行模式: 启动SOCKS5接入，端口 {}", port);
            } catch (NumberFormatException e) {
                logger.error("无效的端口号: {}", args[0]);
                System.exit(1);
            }
        } else if (args.length == 2) {
            // 双参数：协议类型和端口号
            String protocol = args[0].toLowerCase();
            try {
                int port = Integer.parseInt(args[1]);
                
                switch (protocol) {
                    case "socks5":
                        manager.addSocks5Inbound(port);
                        logger.info("命令行模式: 启动SOCKS5接入，端口 {}", port);
                        break;
                    case "http":
                        manager.addHttpInbound(port);
                        logger.info("命令行模式: 启动HTTP接入，端口 {}", port);
                        break;
                    case "both":
                        manager.addSocks5Inbound(port);
                        manager.addHttpInbound(port + 1);
                        logger.info("命令行模式: 启动SOCKS5接入端口 {} 和HTTP接入端口 {}", port, port + 1);
                        break;
                    default:
                        logger.error("不支持的协议类型: {}，支持: socks5, http, both", protocol);
                        System.exit(1);
                }
            } catch (NumberFormatException e) {
                logger.error("无效的端口号: {}", args[1]);
                System.exit(1);
            }
        } else if (args.length == 3) {
            // 三参数：SOCKS5端口、代理服务器地址、代理服务器端口（兼容旧版本）
            try {
                int localPort = Integer.parseInt(args[0]);
                String proxyHost = args[1];
                int proxyPort = Integer.parseInt(args[2]);
                
                manager.addSocks5Inbound(localPort);
                logger.info("兼容模式: 启动SOCKS5接入端口 {}，代理服务器 {}:{}", 
                    localPort, proxyHost, proxyPort);
                
            } catch (NumberFormatException e) {
                logger.error("无效的端口号参数");
                System.exit(1);
            }
        } else if (args.length == 4) {
            // 四参数：SOCKS5端口、HTTP端口、代理服务器地址、代理服务器端口
            try {
                int socks5Port = Integer.parseInt(args[0]);
                int httpPort = Integer.parseInt(args[1]);
                String proxyHost = args[2];
                int proxyPort = Integer.parseInt(args[3]);
                
                manager.addSocks5Inbound(socks5Port);
                manager.addHttpInbound(httpPort);
                logger.info("多组件模式: 启动SOCKS5端口 {} 和HTTP端口 {}，代理服务器 {}:{}", 
                    socks5Port, httpPort, proxyHost, proxyPort);
                
            } catch (NumberFormatException e) {
                logger.error("无效的端口号参数");
                System.exit(1);
            }
        } else {
            printUsage();
            System.exit(1);
        }
    }
    
    /**
     * 打印使用说明
     */
    private static void printUsage() {
        System.out.println("多组件代理客户端使用说明:");
        System.out.println();
        System.out.println("1. 使用配置文件启动:");
        System.out.println("   java -jar proxy-client.jar");
        System.out.println();
        System.out.println("2. 命令行参数启动:");
        System.out.println("   java -jar proxy-client.jar <port>                    # SOCKS5端口");
        System.out.println("   java -jar proxy-client.jar <protocol> <port>        # 指定协议和端口");
        System.out.println("   java -jar proxy-client.jar <socks5_port> <host> <proxy_port>  # 兼容模式");
        System.out.println("   java -jar proxy-client.jar <socks5_port> <http_port> <host> <proxy_port>  # 多组件模式");
        System.out.println();
        System.out.println("支持的协议类型:");
        System.out.println("   socks5  - SOCKS5代理协议");
        System.out.println("   http    - HTTP CONNECT代理协议");
        System.out.println("   both    - 同时启动SOCKS5和HTTP（HTTP端口=SOCKS5端口+1）");
        System.out.println();
        System.out.println("示例:");
        System.out.println("   java -jar proxy-client.jar 1080");
        System.out.println("   java -jar proxy-client.jar socks5 1080");
        System.out.println("   java -jar proxy-client.jar http 8080");
        System.out.println("   java -jar proxy-client.jar both 1080");
        System.out.println("   java -jar proxy-client.jar 1080 8080 localhost 8888");
    }

    /**
     * 解析配置文件相关的命令行参数
     * 支持的格式：
     * - java ProxyClient --config=path/to/config.yml [other args]
     * - java ProxyClient -c path/to/config.yml [other args]
     */
    private static void parseConfigArgs(String[] args) {
        for (int i = 0; i < args.length; i++) {
            String arg = args[i];

            // 处理 --config=path 格式
            if (arg.startsWith("--config=")) {
                String configPath = arg.substring("--config=".length());
                ProxyClientConfigManager.setConfigFilePath(configPath);
                continue;
            }

            // 处理 -c path 格式
            if (arg.equals("-c") && i + 1 < args.length) {
                String configPath = args[i + 1];
                ProxyClientConfigManager.setConfigFilePath(configPath);
                i++; // 跳过下一个参数，因为它是配置文件路径
                continue;
            }
        }
    }
}
