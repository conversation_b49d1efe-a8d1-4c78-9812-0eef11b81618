# 多阶段构建 Dockerfile for Proxy Server
# 第一阶段：构建阶段
FROM maven:3.6.3-openjdk-17-slim AS builder

# 设置工作目录
WORKDIR /app

# 复制Maven配置文件（阿里云镜像源配置）
#国内才使用，国外不用
#COPY maven-settings.xml /root/.m2/settings.xml

# 复制 pom.xml 文件
COPY pom.xml .

# 下载依赖（利用 Docker 缓存层，使用阿里云镜像源）
RUN mvn dependency:go-offline -B

# 复制源代码
COPY src ./src

# 构建应用（使用阿里云镜像源）
RUN mvn clean package -DskipTests

# 第二阶段：运行阶段
FROM openjdk:17-ea-slim

# 设置维护者信息
LABEL maintainer="proxy-server-team"
LABEL version="1.0.0"
LABEL description="Netty-based Multiplex Proxy Server"

# 安装网络工具用于健康检查
#RUN apt-get update && apt-get install -y net-tools && rm -rf /var/lib/apt/lists/*

# 创建非 root 用户，使用固定的UID/GID以避免权限问题
RUN groupadd -r proxyuser --gid=1000 && useradd -r -g proxyuser --uid=1000 proxyuser

# 设置工作目录
WORKDIR /app

# 创建必要的目录
RUN mkdir -p /app/logs && mkdir -p /app/config && \
    chown -R proxyuser:proxyuser /app

# 复制构建好的 JAR 文件
COPY --from=builder /app/target/proxy-server-1.0.0.jar /app/proxy-server-1.0.0.jar
RUN chown proxyuser:proxyuser /app/proxy-server-1.0.0.jar

RUN chown proxyuser:proxyuser /app/config
# 暴露端口
EXPOSE 8888

# 健康检查
#HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
#    CMD netstat -an | grep :8888 | grep LISTEN || exit 1

# 切换到非 root 用户
USER proxyuser

#如果失败，切换root
#USER root

# 启动命令
# 通过系统属性指定配置目录，确保使用挂载的配置文件
ENTRYPOINT ["java", "-Xms256m", "-Xmx1024m", "-XX:+UseG1GC", "-XX:+UseContainerSupport", "-Dconfig.ext.dir=/app/config/", "-jar", "/app/proxy-server-1.0.0.jar"]
