# 多路复用代理系统

基于Maven、Java和Netty实现的高性能多路复用代理系统，支持多客户端并发访问和连接复用。

## 🚀 核心特性

### 🔄 多路复用与连接管理
- **多路复用协议**: 单一连接支持多个并发会话，大幅提升性能
- **多客户端支持**: 支持多个客户端同时连接，互不干扰
- **智能连接池**: 连接复用机制，减少连接建立开销
- **会话管理**: 智能会话ID分配和回收，避免资源泄漏
- **高并发处理**: 优化的并发安全机制，支持大量并发连接

### 🌐 协议与网络支持
- **多协议支持**: 完整兼容SOCKS5和HTTP CONNECT代理协议
- **UDP支持**: SOCKS5 UDP ASSOCIATE命令支持，实现UDP流量代理
- **🚀 多组件架构**: 支持同时启动多种代理接入，统一管理多种协议
- **🔒 SSL/TLS加密**: 完整的SSL/TLS支持，客户端与服务器间加密通信
- **🔐 Basic认证**: 支持proxy-client连接到proxy-server时的Basic认证

### 🛡️ 智能过滤与安全
- **🌍 地理位置过滤**: 基于IP地理位置的智能访问控制，支持中国直连/海外代理分流
- **🚫 恶意内容过滤**: 多层恶意内容检测机制
  - 恶意域名黑名单过滤
  - 恶意关键词检测
  - 海外可疑网站阻止
- **✅ 白名单系统**: 160+个精心筛选的合法海外网站白名单
- **🔄 在线数据更新**: 自动从多个在线威胁情报源更新恶意内容数据
- **📊 GeoIP支持**: 基于APNIC官方数据的高精度IP地理位置判断，支持自动更新

### ⚙️ 配置与管理
- **📁 外部配置目录**: 支持 `config.ext.dir` 统一配置目录管理
- **🔧 灵活配置**: 支持YAML和Properties配置文件，多种配置路径指定方式
- **🚀 启动时初始化**: 系统启动时自动初始化所有配置文件和组件
- **💾 智能缓存**: DNS缓存和IP地理位置缓存，可配置超时时间和大小限制
- **🔄 动态配置**: 支持配置热重载和在线更新

### 📈 性能与监控
- **📊 性能监控**: 实时性能指标收集和统计报告
- **⚡ 高性能**: 优化的并发处理和缓存机制
- **✅ GraalVM Native Image**: 支持编译为原生可执行文件，启动时间<100ms，内存占用<50MB

## 架构说明

```
Chrome浏览器 -> 代理客户端(多路复用) -> 代理服务器(8888) -> 目标服务器
     ↓              ↓                        ↓
  HTTP/SOCKS5   多路复用协议              连接池管理
   (8080/1080)    (单连接多会话)          (连接复用)
```

### 多路复用优势
- **单连接多会话**: 一个TCP连接承载多个HTTP/SOCKS5会话
- **减少连接数**: 大幅减少与代理服务器的连接数量
- **提高性能**: 避免频繁的连接建立和断开
- **资源节约**: 降低系统资源消耗和网络开销

### 支持的协议
- **SOCKS5代理**: 完整SOCKS5协议实现，支持多路复用
- **多路复用协议**: 自研高效多路复用协议（V2版本）
- **连接池管理**: 智能后端连接复用和管理
- **地址过滤**: 支持ALL_PROXY、CHINA_DIRECT、ALL_DIRECT三种模式

## 项目结构

```
├── proxy-client/                    # 🚀 多协议代理客户端
│   ├── pom.xml                      # Maven配置文件
│   ├── README-ADDRESS-FILTER.md     # 地址过滤功能说明
│   ├── README-GRAALVM.md            # ✅ GraalVM Native Image构建指南
│   ├── build-native.bat             # Windows原生构建脚本
│   ├── build-native.sh              # Linux/macOS原生构建脚本
│   └── src/main/java/com/proxy/client/
│       ├── ProxyClient.java         # 🚀 多组件代理客户端主类
│       ├── ProxyClientManager.java  # 代理客户端管理器
│       ├── config/                  # 配置管理模块
│       │   ├── ProxyClientConfigManager.java
│       │   ├── properties/          # 配置属性类
│       │   ├── annotation/          # 配置注解
│       │   └── binder/              # 配置绑定器
│       ├── inbound/                 # 🌐 多协议接入模块
│       │   ├── ProxyInbound.java    # 接入组件接口
│       │   ├── AbstractProxyInbound.java
│       │   └── impl/                # 协议实现
│       │       ├── Socks5Inbound.java    # SOCKS5接入组件
│       │       └── HttpInbound.java      # HTTP接入组件
│       ├── connection/              # 连接管理模块
│       │   ├── ConnectionManager.java
│       │   ├── DirectConnectionHandler.java
│       │   └── SessionHandler.java
│       ├── filter/                  # 🌍 地址过滤模块
│       │   ├── AddressFilter.java
│       │   └── DefaultAddressFilter.java
│       ├── handler/                 # 协议处理器
│       │   ├── MultiplexSocks5Handler.java
│       │   └── MultiplexSessionHandler.java
│       ├── ssl/                     # 🔒 SSL/TLS支持
│       │   └── ClientSslContextManager.java
│       ├── protocol/                # 多路复用协议
│       │   └── MultiplexProtocol.java
│       ├── util/                    # 工具类
│       │   └── GeoIPUtil.java       # IP地理位置判断
│       └── resources/
│           ├── proxy-client.properties # 客户端配置文件
│           └── china-ip-ranges.txt  # 中国IP段数据（可选）
├── proxy-server/                    # 多路复用代理服务器
│   ├── pom.xml
│   └── src/main/java/com/proxy/server/
│       ├── core/                    # 核心组件
│       │   ├── ProxyServer.java     # 服务器启动类
│       │   └── ProxyServerInitializer.java
│       ├── handler/                 # 请求处理器
│       │   ├── MultiplexProxyHandler.java
│       │   └── MultiplexBackendHandler.java
│       ├── protocol/                # 协议处理
│       │   ├── MultiplexProtocol.java
│       │   └── MultiplexProtocolDetector.java
│       ├── pool/                    # 连接池管理
│       │   └── ConnectionPool.java
│       ├── config/                  # 配置管理
│       │   └── ConnectionPoolConfig.java
│       ├── blacklist/               # 黑名单系统
│       │   └── HostBlacklist.java
│       ├── auth/                    # 🔐 认证模块
│       │   └── AuthManager.java
│       ├── ssl/                     # 🔒 SSL/TLS支持
│       └── metrics/                 # 性能监控
│           └── PerformanceMetrics.java
├── configs/                         # ⚙️ 外部配置文件目录
│   ├── development/                 # 开发环境配置
│   │   ├── server/                  # 服务器配置目录
│   │   │   ├── proxy-server.yml     # 服务器主配置文件
│   │   │   ├── china-ip-ranges.txt  # 中国IP段数据
│   │   │   ├── malicious-domains.txt # 恶意域名黑名单
│   │   │   ├── malicious-keywords.txt # 恶意关键词列表
│   │   │   └── whitelist-domains.txt # 白名单域名
│   │   └── client/                  # 客户端配置目录
│   │       ├── proxy-client.yml     # 客户端主配置文件
│   │       └── china-ip-ranges.txt  # 中国IP段数据
│   └── production/                  # 生产环境配置
├── scripts/                         # 🛠️ 工具脚本
│   ├── generate-ssl-certificates.bat # SSL证书生成脚本
│   └── generate-ssl-certificates.sh
├── README.md                        # 项目主文档
├── FEATURES.md                      # 🚀 功能特性详解
├── ROADMAP.md                       # 🗺️ 功能路线图
├── README-BASIC-AUTH.md             # 🔐 Basic认证功能说明
├── MULTI_INBOUND_GUIDE.md           # 🚀 多组件代理接入使用指南
├── QUICK_START_NATIVE.md            # ✅ GraalVM Native Image快速开始指南
├── CORE_ARCHITECTURE.md             # 🏗️ 核心架构说明
├── CONFIGURATION_GUIDE.md           # ⚙️ 配置文件指南
├── SSL_CONFIGURATION_REFERENCE.md   # 🔒 SSL配置参考
└── SSL_DEPLOYMENT_GUIDE.md          # 🚀 SSL部署指南
```

## 🔧 配置概述

### 配置文件结构
系统采用外部配置目录 (`config.ext.dir`) 统一管理所有配置文件：

```
configs/development/
├── sever/                          # 服务器配置目录
│   ├── proxy-server.yml            # 主配置文件
│   ├── china-ip-ranges.txt         # 中国IP段数据 (自动更新)
│   ├── malicious-domains.txt       # 恶意域名黑名单 (在线更新)
│   ├── malicious-keywords.txt      # 恶意关键词列表 (在线更新)
│   └── whitelist-domains.txt       # 白名单域名 (160+合法网站)
└── client/                         # 客户端配置目录
    ├── proxy-client.yml            # 主配置文件
    └── china-ip-ranges.txt         # 中国IP段数据 (自动更新)
```

### 核心配置项

#### 地理位置过滤配置
```yaml
geo-location-filter:
  enable: true                      # 启用地理位置过滤
  block-overseas-suspicious: true   # 阻止海外可疑网站
  enable-domain-filter: true        # 启用域名过滤
  enable-keyword-filter: true       # 启用关键词过滤
  enable-whitelist: true            # 启用白名单
  dns-cache-timeout-minutes: 10     # DNS缓存超时
  ip-cache-timeout-minutes: 120     # IP缓存超时
  max-cache-size: 50000            # 最大缓存条目数
  auto-update-ip-ranges: true       # 自动更新IP段数据
  update-interval-hours: 24         # 更新间隔(小时)
```

#### 配置目录指定
```properties
# application.properties
config.ext.dir=configs/development/sever/    # 服务器配置目录
config.ext.dir=configs/development/client/   # 客户端配置目录
```

### 自动化功能
- **🔄 启动时初始化**: 自动加载所有配置文件和初始化组件
- **📡 在线数据更新**: 定期从威胁情报源更新恶意内容数据
- **💾 智能缓存**: DNS和IP地理位置缓存，提升性能
- **📊 配置验证**: 启动时验证配置文件格式和有效性

## 编译和运行

### 1. 编译项目

```bash
# 编译代理服务器
cd proxy-server
mvn clean compile

# 编译代理客户端
cd ../proxy-client
mvn clean compile
```

### 1.1 ✅ GraalVM Native Image构建（推荐）

**已验证成功**，可以将代理客户端编译为原生可执行文件：

#### Windows（已验证）:
```cmd
cd proxy-client
build-native-final-fix.bat
```

#### Linux/macOS:
```bash
cd proxy-client
chmod +x build-native-final-fix.sh
./build-native-final-fix.sh
```

**Native Image优势**：
- ⚡ **极速启动**: 启动时间 < 100毫秒（vs JVM的2-3秒）
- 💾 **低内存**: 运行时内存占用 < 50MB（vs JVM的100-200MB）
- 📦 **单文件部署**: 无需安装Java环境，单个exe文件即可运行
- 🚀 **即时响应**: 无JVM预热时间，立即达到最佳性能

**快速开始**: [QUICK_START_NATIVE.md](QUICK_START_NATIVE.md) | **详细说明**: [proxy-client/README-GRAALVM.md](proxy-client/README-GRAALVM.md)

### 2. 启动多路复用代理服务器

```bash
# 使用Maven命令启动
cd proxy-server
mvn exec:java -Dexec.mainClass="com.proxy.server.ProxyServer" -Dexec.args="8888"

# 服务器将监听8888端口，支持多客户端并发连接
```

### 3. 启动智能代理客户端

#### 🚀 方式1：多组件模式（✅ 推荐）
```bash
# 使用配置文件启动（同时启动SOCKS5和HTTP）
cd proxy-client
mvn exec:java -Dexec.mainClass="com.proxy.client.ProxyClient"

# 命令行参数启动
mvn exec:java -Dexec.mainClass="com.proxy.client.ProxyClient" -Dexec.args="both 1080"
mvn exec:java -Dexec.mainClass="com.proxy.client.ProxyClient" -Dexec.args="1080 8080 localhost 8888"
```

#### 方式2：Native Image（✅ 推荐，已验证）
```cmd
# Windows - 多组件模式
cd proxy-client
target\proxy-client.exe                    # 使用配置文件
target\proxy-client.exe both 1080          # 同时启动SOCKS5(1080)和HTTP(1081)
target\proxy-client.exe 1080 8080 localhost 8888  # 指定端口

# Linux/macOS
./target/proxy-client both 1080
./target/proxy-client 1080 8080 localhost 8888
```

#### 方式3：单协议模式（兼容旧版本）
```bash
# SOCKS5模式
mvn exec:java -Dexec.mainClass="com.proxy.client.Socks5ProxyClient"
mvn exec:java -Dexec.mainClass="com.proxy.client.ProxyClient" -Dexec.args="socks5 1080"

# HTTP模式
mvn exec:java -Dexec.mainClass="com.proxy.client.ProxyClient" -Dexec.args="http 8080"
```

#### 🚀 多组件客户端特性：
- **多协议支持**：同时提供SOCKS5和HTTP CONNECT代理服务
- **统一管理**：所有协议共享连接管理器和认证机制
- **灵活配置**：支持配置文件和命令行参数配置
- **独立端口**：每个协议使用独立监听端口
- **智能路由**：统一的地址过滤和智能分流
- **资源共享**：单连接支持多协议多会话复用
- **Native Image支持**：完全支持原生编译，性能更优

#### 支持的协议：
- **SOCKS5**：标准SOCKS5代理协议（默认端口1081）
- **HTTP CONNECT**：HTTP隧道代理协议（默认端口8080）

详细使用说明：[MULTI_INBOUND_GUIDE.md](MULTI_INBOUND_GUIDE.md)

### 4. 配置浏览器代理

#### Chrome浏览器SOCKS5代理设置（推荐）
```
SOCKS代理服务器地址: localhost
端口: 1080（或配置文件中设置的端口）
协议: SOCKS5
```

#### 客户端配置文件
编辑 `proxy-client/src/main/resources/proxy-client.properties`：
```properties
# 地址过滤模式
# ALL_PROXY: 所有连接都通过proxy-server转发
# CHINA_DIRECT: 中国地区IP直连，其他通过proxy-server转发
# ALL_DIRECT: 所有连接都直连
filter.mode=CHINA_DIRECT

# 代理服务器配置
proxy.server.host=localhost
proxy.server.port=8888

# 本地SOCKS5监听端口（兼容旧版本）
local.port=1080

# 多组件配置
inbound.enabled=socks5,http
inbound.socks5.port=1081
inbound.socks5.enabled=true
inbound.http.port=8080
inbound.http.enabled=true

# 认证配置（可选）
auth.enable=false
auth.username=admin
auth.password=password
```

#### 🚀 多组件配置说明

**启用组件配置**：
```properties
# 启用的组件类型（逗号分隔）
inbound.enabled=socks5,http    # 同时启用SOCKS5和HTTP
inbound.enabled=socks5         # 仅启用SOCKS5
inbound.enabled=http           # 仅启用HTTP
```

**端口配置**：
```properties
# SOCKS5配置
inbound.socks5.port=1081
inbound.socks5.enabled=true

# HTTP配置
inbound.http.port=8080
inbound.http.enabled=true
```

#### 🔐 Basic认证配置

如需启用认证功能，请参考：[README-BASIC-AUTH.md](README-BASIC-AUTH.md)

**服务器端配置** (`proxy-server/src/main/resources/proxy-server.properties`)：
```properties
auth.enable=true
auth.username=admin
auth.password=your_secure_password
```

**客户端配置** (`proxy-client/src/main/resources/proxy-client.properties`)：
```properties
auth.enable=true
auth.username=admin
auth.password=your_secure_password
```

## ⚙️ 外部配置文件

### 配置文件架构
系统采用分层配置架构，支持灵活的配置文件管理：

```
配置优先级（从高到低）：
1. 命令行参数 (--config=path 或 -c path)
2. 系统属性 (-Dproxy.server.config=path)
3. 环境变量 (PROXY_SERVER_CONFIG=path)
4. Properties文件指定路径
5. 默认配置
```

### 配置文件指定方式

#### 方式1：Properties文件指定（推荐）
编辑 `proxy-server/src/main/resources/application.properties`：
```properties
# YAML配置文件路径
config.file.path=configs/development/proxy-server.yml
```

#### 方式2：命令行参数
```bash
# 启动服务器
mvn -f proxy-server/pom.xml exec:java -Dexec.mainClass="com.proxy.server.ProxyServer" -Dexec.args="--config=configs/production/proxy-server.yml"

# 启动客户端
mvn -f proxy-client/pom.xml exec:java -Dexec.mainClass="com.proxy.client.ProxyClient" -Dexec.args="-c configs/production/proxy-client.yml"
```

#### 方式3：环境变量
```bash
# Windows
set PROXY_SERVER_CONFIG=configs/production/proxy-server.yml
set PROXY_CLIENT_CONFIG=configs/production/proxy-client.yml

# Linux/macOS
export PROXY_SERVER_CONFIG=configs/production/proxy-server.yml
export PROXY_CLIENT_CONFIG=configs/production/proxy-client.yml
```

### 配置文件示例
详细的配置文件说明和示例请参考：[CONFIGURATION_GUIDE.md](CONFIGURATION_GUIDE.md)

#### 多客户端支持
- 可以同时启动多个代理客户端实例
- 每个客户端独立管理自己的多路复用连接
- 支持不同端口和不同过滤模式的多个代理服务

## 参数说明

### 多路复用代理服务器参数
- 参数1: 监听端口 (默认: 8888)
- 支持多客户端并发连接
- 自动协议检测和多路复用处理
- 内置连接池和黑名单系统
- 实时性能监控和统计

### SOCKS5代理客户端参数 (Socks5ProxyClient)
- 参数1: 本地监听端口 (默认: 1080，可通过配置文件设置)
- 参数2: 代理服务器地址 (默认: localhost，可通过配置文件设置)
- 参数3: 代理服务器端口 (默认: 8888，可通过配置文件设置)
- 使用多路复用协议，单连接支持多会话
- 智能地址过滤，支持中国IP直连
- 自动GeoIP数据更新和缓存

## 🎯 核心功能特性

### 多路复用技术
- **单连接多会话**: 一个TCP连接承载多个SOCKS5会话
- **会话管理**: 智能会话ID分配、重用和回收机制
- **并发安全**: 优化的并发处理，支持高并发场景
- **协议兼容**: 完全兼容标准SOCKS5协议

### 智能地址过滤
- **三种过滤模式**: ALL_PROXY（全代理）、CHINA_DIRECT（中国直连）、ALL_DIRECT（全直连）
- **GeoIP判断**: 基于APNIC官方数据的高精度IP地理位置判断
- **在线更新**: 支持从GitHub等在线源获取最新中国IP段数据
- **智能分流**: 中国IP直连提升速度，海外IP走代理保证访问

### 连接池管理
- **智能连接复用**: 后端连接池管理，减少连接建立开销
- **连接状态监控**: 实时监控连接健康状态
- **自动清理**: 智能清理无效和空闲连接
- **资源优化**: 每主机连接数限制，防止资源耗尽

### 🔐 Basic认证系统
- **安全连接**: proxy-client连接到proxy-server时支持Basic认证
- **灵活配置**: 可选启用/禁用，支持自定义用户名密码
- **自动认证**: 客户端自动发送认证请求，无需手动干预
- **超时保护**: 认证超时自动断开连接，防止恶意连接
- **兼容性**: 完全向后兼容，未启用时不影响现有功能

### 黑名单系统
- **智能屏蔽**: 自动识别和屏蔽无法访问的主机
- **快速响应**: 黑名单主机从2秒超时减少到毫秒级拒绝
- **自动恢复**: 成功连接后自动从黑名单移除
- **统计监控**: 详细的黑名单命中统计和效果分析

### 性能监控
- **实时指标**: 连接数、会话数、传输字节数等关键指标
- **统计报告**: 定期输出性能统计报告
- **连接池监控**: 连接复用率、命中率等详细统计
- **资源跟踪**: 内存使用、连接状态等资源监控

### 高性能架构
- **异步I/O**: 基于Netty的高性能异步网络框架
- **事件驱动**: 非阻塞事件驱动架构
- **内存管理**: 优化的ByteBuf管理，减少内存拷贝
- **线程模型**: 高效的Reactor线程模型

### 稳定性保障
- **错误恢复**: 完善的异常处理和错误恢复机制
- **资源清理**: 自动资源清理，防止内存泄漏
- **连接监控**: 详细的连接状态监控和日志
- **并发优化**: 解决高并发场景下的竞态条件问题

## 🔒 SSL/TLS 加密配置

### SSL功能特性
- **双向SSL支持**: 服务器端和客户端完整SSL实现
- **多种认证模式**: 单向认证、双向认证、信任所有证书模式
- **协议版本支持**: TLSv1.2, TLSv1.3
- **证书格式支持**: PKCS12, JKS
- **开发测试友好**: 自签名证书支持

### 快速启用SSL

#### 1. 生成证书
```bash
cd scripts
.\generate-ssl-certificates.bat    # Windows
./generate-ssl-certificates.sh     # Linux/Mac
```

#### 2. 服务器SSL配置
编辑 `proxy-server/src/main/resources/proxy-server.yml`：
```yaml
ssl:
  enable: true
  key-store-path: "server.p12"
  key-store-password: "xiang1"
  protocols: ["TLSv1.2", "TLSv1.3"]
```

#### 3. 客户端SSL配置
编辑 `proxy-client/src/main/resources/proxy-client.yml`：
```yaml
ssl:
  enable: true
  trust-all: true              # 开发测试模式
  verify-hostname: false       # localhost测试
```

### SSL配置模式

**开发测试模式**:
- 使用自签名证书
- 信任所有证书 (`trust-all: true`)
- 禁用主机名验证
- 单向SSL认证

**生产环境模式**:
- 使用CA签名证书
- 严格证书验证 (`trust-all: false`)
- 启用主机名验证
- 双向SSL认证

### 详细文档
- [SSL配置指南](proxy-server/SSL_SETUP_GUIDE.md)
- [SSL部署指南](SSL_DEPLOYMENT_GUIDE.md)
- [SSL配置参考](SSL_CONFIGURATION_REFERENCE.md)

## 🔧 多路复用协议详解

### 协议设计原理
多路复用代理系统使用自研的高效多路复用协议，在单一TCP连接上承载多个并发会话：

#### 协议特性
1. **会话隔离**: 每个HTTP/SOCKS5请求分配独立的会话ID
2. **数据封装**: 将原始数据封装为多路复用数据包
3. **流量控制**: 智能的会话级流量控制和拥塞控制
4. **错误处理**: 会话级错误隔离，单个会话异常不影响其他会话

#### 数据包格式
```
+--------+--------+--------+--------+
| Magic  | Type   | SessionID       |
+--------+--------+--------+--------+
| Data Length     | Data...         |
+--------+--------+--------+--------+
```

### 协议优势
- **连接复用**: 大幅减少TCP连接数量
- **延迟优化**: 避免频繁的连接建立和断开
- **带宽效率**: 减少TCP握手和慢启动开销
- **资源节约**: 降低系统文件描述符使用

### 兼容性保障
- **透明代理**: 对浏览器和应用程序完全透明
- **协议兼容**: 完全兼容HTTP CONNECT和SOCKS5协议
- **自动降级**: 在不支持多路复用时自动降级为传统代理

## 🏊 智能连接池管理

### 连接池架构
代理服务器实现了高效的后端连接池，支持连接复用和智能管理：

#### 核心特性
- **按主机分组**: 每个目标主机(host:port)维护独立连接池
- **连接复用**: 自动复用空闲连接，减少连接建立开销
- **状态监控**: 实时监控连接健康状态，自动清理无效连接
- **并发安全**: 线程安全的连接获取和归还机制
- **资源限制**: 防止连接数过多导致资源耗尽

#### 连接生命周期
1. **创建**: 按需创建新的后端连接
2. **使用**: 分配给特定会话使用
3. **归还**: 会话结束后归还到连接池
4. **复用**: 下次相同主机请求时复用连接
5. **清理**: 自动清理无效和过期连接

### 配置参数
在 `ConnectionPoolConfig.java` 中的优化配置：

```java
// 每个主机的最大连接数（支持高并发）
MAX_CONNECTIONS_PER_HOST = 20

// 连接空闲超时时间（60秒）
CONNECTION_IDLE_TIMEOUT = 60000

// 连接建立超时时间（5秒，快速失败）
CONNECTION_TIMEOUT = 5000

// 是否启用连接池
ENABLE_CONNECTION_POOL = true
```

### 性能监控
连接池提供详细的统计信息：
- 各主机的池中连接数
- 连接创建和复用统计
- 连接健康状态监控
- 资源使用情况分析

## 📈 性能优势

### 多路复用带来的性能提升
- **连接数减少**: 单连接承载多会话，连接数减少90%以上
- **延迟降低**: 避免频繁的TCP握手，响应时间提升50%
- **吞吐量提升**: 减少连接管理开销，整体吞吐量提升3-5倍
- **资源节约**: 大幅降低文件描述符和内存使用

### 连接池优化效果
- **连接复用率**: 后端连接复用率可达80%以上
- **建立延迟**: 复用连接避免TCP握手，延迟降低100-200ms
- **并发能力**: 支持每客户端200+并发会话
- **稳定性**: 优化的并发安全机制，支持高负载场景

### 适用场景
- **浏览器代理**: 多标签页并发访问，性能提升显著
- **API调用**: 大量API请求的应用程序
- **爬虫程序**: 高并发网页抓取
- **企业网关**: 内网到外网的统一代理

## 🛠️ 技术栈

- **Java 11**: 现代Java特性支持
- **Maven 3.x**: 项目构建和依赖管理
- **Netty 4.1.100.Final**: 高性能异步网络框架
- **✅ GraalVM Native Image**: 原生可执行文件编译，极速启动和低内存占用
- **SLF4J + Logback**: 结构化日志系统
- **自研多路复用协议**: 高效的会话管理（V2版本）
- **智能连接池**: 优化的连接复用机制
- **GeoIP技术**: 基于APNIC数据的IP地理位置判断
- **并发优化**: ConcurrentHashMap、LongAdder等高性能并发组件

### 部署选项
- **JVM部署**: 传统Java应用部署方式，需要Java运行环境
- **✅ Native Image部署**: 单文件原生可执行文件，无需Java环境，性能更优

## 🔧 故障排除

### 常见问题
1. **连接错误**: 检查服务器是否启动，端口是否被占用
2. **地址过滤问题**: 检查配置文件设置，查看GeoIP数据是否正确加载
3. **IP判断不准确**: 更新IP段数据，检查在线数据源连接
4. **性能问题**: 查看性能监控日志，调整连接池和黑名单参数

### 日志分析
- **DEBUG级别**: 详细的会话和连接管理信息、IP判断过程
- **INFO级别**: 连接建立和关闭事件、地址过滤决策、性能统计
- **WARN级别**: 异常情况和性能警告、配置文件加载失败
- **ERROR级别**: 严重错误和异常堆栈

### 性能调优
- 调整 `MAX_SESSIONS_PER_CLIENT` 限制并发会话数（默认200）
- 优化 `MAX_CONNECTIONS_PER_HOST` 控制连接池大小（默认20）
- 监控会话ID重用情况，确保资源高效利用
- 定期更新中国IP段数据，保证地址过滤准确性
- 观察黑名单命中率，调整失败阈值和缓存时间

## 📚 相关文档

### 配置文档
- [CONFIGURATION_GUIDE.md](CONFIGURATION_GUIDE.md) - ⚙️ 配置文件指南
- [CONFIG_EXAMPLES.md](CONFIG_EXAMPLES.md) - 📋 配置文件示例
- [README-BASIC-AUTH.md](README-BASIC-AUTH.md) - 🔐 Basic认证配置

### SSL/TLS文档
- [SSL_CONFIGURATION_REFERENCE.md](SSL_CONFIGURATION_REFERENCE.md) - 🔒 SSL配置参考
- [SSL_DEPLOYMENT_GUIDE.md](SSL_DEPLOYMENT_GUIDE.md) - 🚀 SSL部署指南
- [proxy-server/SSL_SETUP_GUIDE.md](proxy-server/SSL_SETUP_GUIDE.md) - 🛠️ SSL设置指南

### 功能文档
- [FEATURES.md](FEATURES.md) - 🚀 功能特性详解
- [MULTI_INBOUND_GUIDE.md](MULTI_INBOUND_GUIDE.md) - 🚀 多组件代理接入指南
- [proxy-client/README-ADDRESS-FILTER.md](proxy-client/README-ADDRESS-FILTER.md) - 🌍 地址过滤功能
- [proxy-client/README-GRAALVM.md](proxy-client/README-GRAALVM.md) - ⚡ GraalVM Native Image

### 快速开始
- [QUICK_START_NATIVE.md](QUICK_START_NATIVE.md) - 🚀 Native Image快速开始
- [CORE_ARCHITECTURE.md](CORE_ARCHITECTURE.md) - 🏗️ 核心架构说明

### 版本信息
- [CHANGELOG.md](CHANGELOG.md) - 📝 更新日志