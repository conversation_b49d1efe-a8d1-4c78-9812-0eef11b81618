# 🐳 Proxy Server 简化 Docker 部署指南

本指南介绍如何使用 Docker 快速部署 Netty 多路复用代理服务器。

## 📋 目录

- [快速开始](#快速开始)
- [环境要求](#环境要求)
- [配置说明](#配置说明)
- [部署步骤](#部署步骤)
- [管理命令](#管理命令)
- [故障排除](#故障排除)

## 🚀 快速开始

### 一键启动

```bash
# Linux/macOS
./build-and-run.sh build
./build-and-run.sh start

# Windows
build-and-run.bat build
build-and-run.bat start
```

### 验证部署

```bash
# 查看服务状态
./build-and-run.sh status

# 测试代理连接
curl -x ****************************************************** http://httpbin.org/ip
```

## 🔧 环境要求

### 系统要求
- **操作系统**: Linux, macOS, Windows 10+
- **CPU**: 1 核心以上
- **内存**: 2GB 以上
- **磁盘**: 10GB 可用空间

### 软件依赖
- **Docker**: 20.10+ 
- **Docker Compose**: 2.0+

### 安装 Docker

#### Linux (Ubuntu/Debian)
```bash
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER
```

#### macOS
```bash
# 使用 Homebrew
brew install --cask docker

# 或下载 Docker Desktop
# https://www.docker.com/products/docker-desktop
```

#### Windows
下载并安装 Docker Desktop: https://www.docker.com/products/docker-desktop

## ⚙️ 配置说明

### 项目结构

```
netty_proxy_Multiplex/
├── proxy-server/
│   ├── Dockerfile              # Docker 构建文件
│   ├── pom.xml                # Maven 配置
│   └── src/                   # 源代码
├── configs/
│   └── production/
│       └── server/            # 生产环境配置（挂载到容器 /app/config）
│           ├── proxy-server.yml        # 主配置文件
│           ├── china-ip-ranges.txt     # 中国IP段
│           ├── malicious-domains.txt   # 恶意域名黑名单
│           ├── malicious-keywords.txt  # 恶意关键词
│           └── whitelist-domains.txt   # 白名单域名
├── logs/                      # 日志目录（挂载到容器 /app/logs）
├── docker-compose.yml         # Docker Compose 配置
├── build-and-run.sh          # Linux/macOS 管理脚本
└── build-and-run.bat         # Windows 管理脚本
```

### 配置文件路径映射

Docker 容器启动时的路径映射关系：

| 宿主机路径 | 容器内路径 | 说明 |
|-----------|-----------|------|
| `./configs/production/server/` | `/app/config/` | 配置文件目录（只读） |
| `./logs/` | `/app/logs/` | 日志文件目录（读写） |

### Maven 构建优化

项目使用阿里云Maven镜像源来加速依赖下载：

- **配置文件**: `proxy-server/maven-settings.xml`
- **镜像源**: `https://maven.aliyun.com/repository/public`
- **优势**: 国内网络环境下构建速度更快，避免依赖下载超时

### 主要配置文件

#### proxy-server.yml
```yaml
# 服务器配置
server:
  port: 8888

# 认证配置
auth:
  enable: true
  username: admin
  password: secure_production_password  # 请修改为安全密码

# 连接池配置
pool:
  enable: true
  max-connections:
    per-host: 50

# SSL配置（可选）
ssl:
  enable: false  # 设置为 true 启用 SSL
```

## 🚢 部署步骤

### 1. 准备环境

```bash
# 克隆或下载项目
cd netty_proxy_Multiplex

# 确保脚本有执行权限（Linux/macOS）
chmod +x build-and-run.sh
```

### 2. 修改配置（可选）

编辑配置文件 `configs/production/server/proxy-server.yml`：

```bash
# Linux/macOS
nano configs/production/server/proxy-server.yml

# Windows
notepad configs/production/server/proxy-server.yml
```

重要配置项：
- `auth.password`: 修改默认密码
- `ssl.enable`: 是否启用 SSL
- `geo-location-filter`: 地理位置过滤设置

### 3. 构建镜像

```bash
# Linux/macOS
./build-and-run.sh build

# Windows
build-and-run.bat build
```

**注意**: 构建过程中会使用阿里云Maven镜像源来加速依赖下载，首次构建可能需要几分钟时间。

### 4. 启动服务

```bash
# Linux/macOS
./build-and-run.sh start

# Windows
build-and-run.bat start
```

### 5. 验证部署

```bash
# 查看服务状态
./build-and-run.sh status

# 查看日志
./build-and-run.sh logs
```

## 🛠️ 管理命令

### 基本命令

```bash
# 构建镜像
./build-and-run.sh build

# 启动服务
./build-and-run.sh start

# 停止服务
./build-and-run.sh stop

# 重启服务
./build-and-run.sh restart

# 查看状态
./build-and-run.sh status

# 查看日志
./build-and-run.sh logs

# 清理资源
./build-and-run.sh clean

# 显示帮助
./build-and-run.sh help
```

### Docker Compose 原生命令

```bash
# 启动服务（后台运行）
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f proxy-server

# 停止服务
docker-compose down

# 重新构建并启动
docker-compose up -d --build
```

## 🔍 故障排除

### 常见问题

#### 1. 端口被占用

**错误信息**: `bind: address already in use`

**解决方案**:
```bash
# 查看端口占用
netstat -tulpn | grep :8888  # Linux
netstat -an | findstr :8888  # Windows

# 修改端口（编辑 docker-compose.yml）
ports:
  - "9999:8888"  # 将主机端口改为 9999
```

#### 2. 容器启动失败

**解决方案**:
```bash
# 查看详细日志
docker-compose logs proxy-server

# 检查配置文件语法
docker run --rm -v $(pwd)/configs/production/server:/config mikefarah/yq eval /config/proxy-server.yml
```

#### 3. 内存不足

**解决方案**:
```bash
# 检查系统资源
docker stats

# 调整内存限制（编辑 docker-compose.yml）
deploy:
  resources:
    limits:
      memory: 1G  # 降低内存限制
```

#### 4. 配置文件权限问题

**解决方案**:
```bash
# 检查文件权限
ls -la configs/production/server/

# 修复权限
chmod 644 configs/production/server/*
```

### 健康检查

```bash
# 检查容器健康状态
docker inspect --format='{{.State.Health.Status}}' proxy-server

# 手动测试代理功能
curl -x ***************************************** http://httpbin.org/ip

# 检查端口监听
docker exec proxy-server netstat -an | grep :8888
```

### 日志分析

```bash
# 查看实时日志
docker-compose logs -f proxy-server

# 查看最近的错误日志
docker-compose logs --tail 50 proxy-server | grep ERROR

# 导出日志到文件
docker-compose logs proxy-server > proxy-server.log
```

## 📊 监控和维护

### 性能监控

```bash
# 查看容器资源使用
docker stats proxy-server

# 查看系统资源
free -h  # Linux
top      # Linux/macOS
```

### 配置更新

```bash
# 修改配置文件后重启服务
./build-and-run.sh restart

# 或者重新加载配置（如果支持）
docker-compose exec proxy-server kill -HUP 1
```

### 数据备份

```bash
# 备份配置文件
tar -czf proxy-config-backup-$(date +%Y%m%d).tar.gz configs/

# 备份日志
tar -czf proxy-logs-backup-$(date +%Y%m%d).tar.gz logs/
```

## 🔐 安全建议

1. **修改默认密码**: 编辑 `proxy-server.yml` 中的 `auth.password`
2. **启用 SSL**: 设置 `ssl.enable: true` 并配置证书
3. **限制访问**: 使用防火墙限制访问端口
4. **定期更新**: 定期重新构建镜像以获取安全更新
5. **监控日志**: 定期检查日志中的异常活动

## 📝 配置示例

### 启用 SSL 配置

```yaml
ssl:
  enable: true
  key-store-path: "/app/config/server.p12"
  key-store-password: "your_keystore_password"
  protocols:
    - "TLSv1.2"
    - "TLSv1.3"
```

### 自定义端口配置

```yaml
# docker-compose.yml
ports:
  - "9999:8888"  # 主机端口:容器端口

# proxy-server.yml
server:
  port: 8888  # 容器内端口保持不变
```

## 🆘 获取帮助

如果遇到问题：

1. 查看本文档的故障排除部分
2. 检查容器日志: `./build-and-run.sh logs`
3. 验证配置文件语法
4. 检查系统资源使用情况
5. 提供详细的错误信息和环境描述
