# SSL/TLS 配置指南

本指南介绍如何为 proxy-server 和 proxy-client 配置和启用 SSL/TLS 支持。

## 🔐 功能特性

- **双向SSL/TLS支持** - 服务器端和客户端完整SSL实现
- **多协议版本支持** - TLSv1.2, TLSv1.3
- **灵活的认证模式** - 单向认证、双向认证、信任所有证书模式
- **多种证书格式** - 支持PKCS12和JKS密钥库格式
- **自签名证书支持** - 开发测试环境快速部署
- **生产环境支持** - CA签名证书和严格验证模式

## 🚀 快速开始

### 1. 使用自动化脚本生成证书（推荐）

**Windows:**
```bash
cd scripts
.\generate-ssl-certificates.bat
```

**Linux/Mac:**
```bash
cd scripts
chmod +x generate-ssl-certificates.sh
./generate-ssl-certificates.sh
```

### 2. 手动生成证书（可选）

```bash
# 生成服务器证书
keytool -genkeypair -alias server -keyalg RSA -keysize 2048 -storetype PKCS12 \
  -keystore server.p12 -storepass xiang1 -keypass xiang1 -validity 365 \
  -dname "CN=localhost,OU=Development,O=ProxyServer,L=Beijing,ST=Beijing,C=CN" \
  -ext SAN=dns:localhost,ip:127.0.0.1

# 生成客户端证书（双向认证用）
keytool -genkeypair -alias client -keyalg RSA -keysize 2048 -storetype PKCS12 \
  -keystore client.p12 -storepass xiang1 -keypass xiang1 -validity 365 \
  -dname "CN=proxy-client,OU=Development,O=ProxyClient,L=Beijing,ST=Beijing,C=CN"

# 创建信任库
keytool -exportcert -alias server -keystore server.p12 -storepass xiang1 -file server.crt
keytool -importcert -alias server -keystore truststore.p12 -storepass xiang1 -file server.crt -noprompt
```

### 3. 配置服务器SSL

编辑 `proxy-server/src/main/resources/proxy-server.yml` 文件：

```yaml
ssl:
  enable: true  # 启用SSL
  key-store-path: "server.p12"
  key-store-password: "xiang1"
  key-store-type: "PKCS12"
  trust-store-path: "truststore.p12"  # 双向认证时使用
  trust-store-password: "xiang1"
  protocols:
    - "TLSv1.2"
    - "TLSv1.3"
  client-auth: false  # 单向认证
  handshake-timeout-seconds: 30
```

### 4. 配置客户端SSL

编辑 `proxy-client/src/main/resources/proxy-client.yml` 文件：

**方案A: 信任所有证书（开发测试）**
```yaml
ssl:
  enable: true
  trust-all: true  # 信任所有证书
  verify-hostname: false  # 禁用主机名验证
  handshake-timeout-seconds: 30
```

**方案B: 使用信任库（更安全）**
```yaml
ssl:
  enable: true
  trust-all: false
  trust-store-path: "truststore.p12"
  trust-store-password: "xiang1"
  verify-hostname: false  # localhost测试时禁用
  handshake-timeout-seconds: 30
```

### 5. 启动SSL服务

**启动服务器:**
```bash
cd proxy-server
mvn clean compile
mvn exec:java -Dexec.mainClass="com.proxy.server.ProxyServer"
```

**启动客户端:**
```bash
cd proxy-client
mvn clean compile
mvn exec:java -Dexec.mainClass="com.proxy.client.Socks5ProxyClient"
```

**服务器启动日志示例:**
```
SSL配置: SSL: enabled, keyStore=server.p12, protocols=[TLSv1.2, TLSv1.3], clientAuth=none
代理服务器启动成功，监听端口: 8888
```

**客户端启动日志示例:**
```
Client SSL: enabled, trustAll=true, protocols=[TLSv1.2, TLSv1.3], verifyHostname=false
SOCKS5代理客户端启动成功，监听端口: 1081
```

## 📋 配置选项详解

### 基本SSL配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `ssl.enable` | boolean | false | 是否启用SSL/TLS |
| `ssl.key-store-path` | string | "server.p12" | 密钥库文件路径 |
| `ssl.key-store-password` | string | "changeit" | 密钥库密码 |
| `ssl.key-store-type` | string | "PKCS12" | 密钥库类型 |

### 高级SSL配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `ssl.protocols` | array | ["TLSv1.2", "TLSv1.3"] | 支持的协议版本 |
| `ssl.cipher-suites` | array | [] | 密码套件（空=默认） |
| `ssl.handshake-timeout-seconds` | int | 30 | 握手超时时间 |

### 客户端认证配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `ssl.client-auth` | boolean | false | 是否启用客户端认证 |
| `ssl.need-client-auth` | boolean | false | 强制客户端认证 |
| `ssl.want-client-auth` | boolean | false | 可选客户端认证 |
| `ssl.trust-store-path` | string | "" | 信任库路径 |
| `ssl.trust-store-password` | string | "changeit" | 信任库密码 |

## 🔧 证书管理

### 生产环境证书

1. **获取CA签名证书**
   ```bash
   # 生成证书签名请求
   keytool -certreq -alias server -keystore server.p12 -file server.csr
   
   # 将CSR发送给CA进行签名
   # 获得签名证书后导入
   keytool -importcert -alias server -keystore server.p12 -file server.crt
   ```

2. **证书链导入**
   ```bash
   # 导入根证书
   keytool -importcert -alias root -keystore server.p12 -file root.crt
   
   # 导入中间证书
   keytool -importcert -alias intermediate -keystore server.p12 -file intermediate.crt
   ```

### 客户端认证设置

1. **生成客户端证书**
   ```bash
   keytool -genkeypair -alias client -keyalg RSA -keysize 2048 -storetype PKCS12 \
     -keystore client.p12 -storepass changeit -validity 365 -dname "CN=client"
   ```

2. **配置信任库**
   ```yaml
   ssl:
     client-auth: true
     need-client-auth: true
     trust-store-path: "truststore.p12"
     trust-store-password: "changeit"
   ```

## 🧪 测试SSL连接

### 使用OpenSSL测试

```bash
# 测试SSL连接
openssl s_client -connect localhost:8888 -servername localhost

# 查看证书信息
openssl s_client -connect localhost:8888 -showcerts
```

### 使用Java测试

运行内置的SSL测试：

```bash
mvn test -Dtest=SslTest
```

或者运行测试主方法：

```bash
mvn exec:java -Dexec.mainClass="com.proxy.server.ssl.SslTest"
```

## 🔍 故障排除

### 常见问题

1. **证书路径建立失败**
   ```
   错误: unable to find valid certification path to requested target
   解决: 客户端配置 trust-all: true 或正确配置信任库
   ```

2. **主机名验证失败**
   ```
   错误: Hostname localhost not verified
   解决: 客户端配置 verify-hostname: false
   ```

3. **证书文件未找到**
   ```
   错误: Cannot load keystore: server.p12
   解决: 确保证书文件在classpath中或使用绝对路径
   ```

4. **密码错误**
   ```
   错误: keystore password was incorrect
   解决: 检查key-store-password配置，默认密码为 xiang1
   ```

5. **协议不支持**
   ```
   错误: SSLException: Received fatal alert: protocol_version
   解决: 检查客户端和服务器支持的协议版本
   ```

### 调试SSL

启用SSL调试日志：

```bash
java -Djavax.net.debug=ssl:handshake:verbose -jar proxy-server.jar
```

### 日志级别配置

在 `logback.xml` 中添加：

```xml
<logger name="com.proxy.server.ssl" level="DEBUG"/>
<logger name="io.netty.handler.ssl" level="DEBUG"/>
```

## 🛡️ 安全建议

1. **协议版本**
   - 仅启用 TLSv1.2 和 TLSv1.3
   - 禁用较旧的协议版本

2. **密码套件**
   - 使用强加密算法
   - 定期更新密码套件配置

3. **证书管理**
   - 使用CA签名的证书（生产环境）
   - 定期更新证书
   - 妥善保管私钥

4. **客户端认证**
   - 敏感环境启用双向认证
   - 定期轮换客户端证书

## 📚 相关文档

- [SSL配置参考手册](../SSL_CONFIGURATION_REFERENCE.md) - 详细的配置选项说明
- [SSL部署指南](../SSL_DEPLOYMENT_GUIDE.md) - 不同环境的部署方案
- [SSL故障排除指南](../SSL_TROUBLESHOOTING.md) - 常见问题解决方案

## 📚 外部参考资料

- [Netty SSL/TLS 文档](https://netty.io/wiki/sslcontextbuilder-and-private-key.html)
- [Java SSL/TLS 指南](https://docs.oracle.com/javase/8/docs/technotes/guides/security/jsse/JSSERefGuide.html)
- [OpenSSL 命令参考](https://www.openssl.org/docs/man1.1.1/man1/openssl.html)
