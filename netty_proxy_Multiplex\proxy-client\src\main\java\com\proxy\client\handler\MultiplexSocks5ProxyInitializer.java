package com.proxy.client.handler;

import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.socket.SocketChannel;

/**
 * 基于多路复用的SOCKS5代理初始化器
 */
public class MultiplexSocks5ProxyInitializer extends ChannelInitializer<SocketChannel> {
    
    @Override
    protected void initChannel(SocketChannel ch) throws Exception {
        ChannelPipeline pipeline = ch.pipeline();
        
        // 添加多路复用SOCKS5处理器
        pipeline.addLast(new MultiplexSocks5Handler());
    }
}
