# Windows全局流量代理使用指南

本指南介绍如何使用proxy_win实现Windows系统的全局流量拦截和代理转发。

## 🎯 系统概述

### 架构组件
1. **proxy-server**: Java实现的多路复用代理服务器
2. **proxy-client**: Java实现的SOCKS5代理客户端
3. **proxy_win**: C++实现的Windows全局流量拦截器

### 数据流向
```
Windows应用程序 → proxy_win → proxy-client → proxy-server → 目标服务器
```

## 🚀 快速开始

### 1. 启动代理服务器
```bash
cd proxy-server
mvn exec:java -Dexec.mainClass="com.proxy.server.ProxyServer" -Dexec.args="8888"
```

### 2. 启动代理客户端
```bash
cd proxy-client
mvn exec:java -Dexec.mainClass="com.proxy.client.ProxyClient" -Dexec.args="1080"
```

### 3. 编译proxy_win
```cmd
cd proxy_win
build.bat
```

### 4. 配置proxy_win
编辑 `proxy_win/proxy_win/proxy_win/config.ini`:
```ini
[SOCKS5 Proxy Settings]
socks5_host=localhost
socks5_port=1080

[Network Interception Settings]
enable_interception=true
interception_method=rawsocket
```

### 5. 启动全局代理
```cmd
# 以管理员身份运行
cd proxy_win\proxy_win\x64\Release
proxy_win.exe
```

## ⚙️ 详细配置

### proxy-server配置
```yaml
# proxy-server/src/main/resources/proxy-server.yml
server:
  port: 8888
  
connection-pool:
  max-connections-per-host: 20
  connection-idle-timeout: 60000
  
auth:
  enable: false
  username: admin
  password: password
```

### proxy-client配置
```yaml
# proxy-client/src/main/resources/proxy-client.yml
proxy:
  server:
    host: localhost
    port: 8888

filter:
  mode: CHINA_DIRECT  # ALL_PROXY, CHINA_DIRECT, ALL_DIRECT

inbound:
  socks5:
    - name: "socks5-main"
      port: 1080
      enabled: true
```

### proxy_win配置
```ini
# proxy_win/proxy_win/proxy_win/config.ini
[SOCKS5 Proxy Settings]
socks5_host=localhost
socks5_port=1080

[Network Interception Settings]
enable_interception=true
interception_method=rawsocket

[Traffic Filtering Rules]
proxy_all_traffic=false
exclude_list=127.0.0.1,localhost,***********/16,10.0.0.0/8,**********/12
include_list=
```

## 🔧 高级配置

### 1. 启用认证
如果需要在proxy-client和proxy-server之间启用认证：

**proxy-server配置**:
```yaml
auth:
  enable: true
  username: admin
  password: your_secure_password
```

**proxy-client配置**:
```yaml
auth:
  enable: true
  username: admin
  password: your_secure_password
```

### 2. SSL/TLS加密
启用客户端和服务器之间的SSL加密：

**生成证书**:
```bash
cd scripts
./generate-ssl-certificates.sh
```

**proxy-server配置**:
```yaml
ssl:
  enable: true
  key-store-path: "server.p12"
  key-store-password: "password"
```

**proxy-client配置**:
```yaml
ssl:
  enable: true
  trust-all: true  # 开发环境
  verify-hostname: false
```

### 3. 地址过滤模式

#### ALL_PROXY模式
所有流量都通过代理服务器：
```yaml
filter:
  mode: ALL_PROXY
```

#### CHINA_DIRECT模式（推荐）
中国IP直连，海外IP代理：
```yaml
filter:
  mode: CHINA_DIRECT
```

#### ALL_DIRECT模式
所有流量都直连（调试用）：
```yaml
filter:
  mode: ALL_DIRECT
```

## 🛠️ 流量拦截方法

### 1. Raw Socket（默认）
- **优点**: 无需额外驱动，兼容性好
- **缺点**: 需要管理员权限，功能有限
- **配置**: `interception_method=rawsocket`

### 2. WinDivert（推荐）
- **优点**: 性能最好，功能最完整
- **缺点**: 需要安装WinDivert驱动
- **安装**: 下载WinDivert并安装驱动
- **配置**: `interception_method=windivert`

### 3. WinPcap/Npcap
- **优点**: 功能完整，稳定性好
- **缺点**: 需要安装WinPcap或Npcap
- **安装**: 下载并安装Npcap
- **配置**: `interception_method=winpcap`

## 📊 监控和调试

### 1. 查看连接状态
proxy-server会定期输出性能统计：
```
=== Performance Metrics ===
Active clients: 1
Total sessions: 5
Active sessions: 2
Bytes sent: 1024000
Bytes received: 2048000
Connection pool stats:
  google.com:443 - Pool: 2, Active: 1
```

### 2. 启用详细日志
在各组件的配置文件中启用DEBUG日志：
```yaml
logging:
  level:
    com.proxy: DEBUG
```

### 3. proxy_win调试
```ini
[Advanced Settings]
verbose_logging=true
```

## 🔍 故障排除

### 常见问题

#### 1. proxy_win无法启动
**错误**: "Failed to create raw socket"
**解决**: 以管理员身份运行proxy_win.exe

#### 2. 连接proxy-client失败
**错误**: "Failed to connect to SOCKS5 proxy server"
**检查**:
- proxy-client是否启动
- 端口1080是否被占用
- 防火墙是否阻止连接

#### 3. 流量没有被拦截
**检查**:
- 是否以管理员权限运行
- 网络接口配置是否正确
- 过滤规则是否正确

#### 4. 代理连接失败
**错误**: "SOCKS5 connect failed"
**检查**:
- proxy-server是否启动
- proxy-client到proxy-server的连接
- 认证配置是否匹配

### 调试步骤

1. **测试各组件连接**:
   ```bash
   # 测试proxy-server
   telnet localhost 8888
   
   # 测试proxy-client SOCKS5
   curl --socks5 localhost:1080 http://httpbin.org/ip
   ```

2. **查看日志输出**:
   - proxy-server: 控制台输出
   - proxy-client: 控制台输出
   - proxy_win: 控制台输出

3. **网络抓包分析**:
   使用Wireshark抓包分析网络流量

## 📈 性能优化

### 1. 连接池优化
```yaml
# proxy-server配置
connection-pool:
  max-connections-per-host: 50  # 增加连接数
  connection-idle-timeout: 120000  # 延长超时
```

### 2. 并发优化
```yaml
# proxy-client配置
performance:
  max-sessions-per-client: 500  # 增加会话数
  worker-threads: 8  # 增加工作线程
```

### 3. proxy_win优化
```ini
[Advanced Settings]
max_connections=200  # 增加最大连接数
connection_timeout=3000  # 减少超时时间
```

## 🔒 安全考虑

### 1. 网络安全
- 使用SSL/TLS加密传输
- 启用认证机制
- 定期更新证书

### 2. 系统安全
- 以最小权限运行
- 监控系统资源使用
- 定期检查日志

### 3. 访问控制
- 配置合适的过滤规则
- 限制代理访问范围
- 监控异常流量

## 📚 相关文档

- [项目总体介绍](README.md)
- [proxy-client使用指南](proxy-client/README.md)
- [proxy_win详细说明](proxy_win/README.md)
- [系统架构图](SYSTEM_ARCHITECTURE_DIAGRAM.md)
- [配置文件指南](CONFIGURATION_GUIDE.md)

## 🔄 更新和维护

### 定期维护任务
1. 更新IP段数据（proxy-client自动更新）
2. 检查证书有效期
3. 监控系统性能
4. 更新安全配置

### 版本升级
1. 备份当前配置
2. 停止所有组件
3. 更新代码和重新编译
4. 恢复配置并测试

---

**注意**: 本系统需要管理员权限运行，会拦截系统所有网络流量，建议在测试环境中充分验证后再在生产环境使用。