# 代理客户端配置文件

# 地址过滤模式
# ALL_PROXY: 所有连接都通过proxy-server转发
# CHINA_DIRECT: 中国地区IP直连，其他通过proxy-server转发
# ALL_DIRECT: 所有连接都直连
filter:
  mode: ALL_PROXY

# 代理服务器配置
proxy:
  server:
#dev
    host: localhost
#    host: ************
    port: 8888

# 本地端口（向后兼容）
local:
  port: 1081

# 认证配置
auth:
  enable: true
  username: admin
  password: password11
  timeout:
    seconds: 30

# 性能配置
performance:
  # 工作线程数 (0表示自动计算，基于CPU核心数)
  worker-threads: 0
  # 直连工作线程数 (0表示自动计算，基于CPU核心数)
  direct-connection-threads: 0

# 接入器配置 - 支持多个同类型接入器
inbound:
  # SOCKS5 接入器列表
  socks5:
    - name: "socks5-main"
      port: 1081
      enabled: true
      description: "主要SOCKS5代理"
    - name: "socks5-backup"
      port: 1083
      enabled: false
      description: "备用SOCKS5代理"
    - name: "socks5-special"
      port: 1084
      enabled: false
      description: "特殊用途SOCKS5代理"
  
  # HTTP 接入器列表
  http:
    - name: "http-main"
      port: 1082
      enabled: true
      description: "主要HTTP代理"
    - name: "http-backup"
      port: 1085
      enabled: false
      description: "备用HTTP代理"
    - name: "http-special"
      port: 1086
      enabled: false
      description: "特殊用途HTTP代理"

# SSL/TLS配置 - 客户端连接到代理服务器的SSL设置
#执行scripts/gen-ssl-certs.bat 自动生成证书
ssl:
  enable: true  # 是否启用SSL/TLS连接到代理服务器
  trust-all: false  # 是否信任所有证书（仅用于测试，不推荐生产使用）
  trust-store-path: "truststore.p12"  # 信任库路径（空表示使用系统默认）
  trust-store-password: "xiang1"  # 信任库密码
  trust-store-type: "PKCS12"  # 信任库类型
  key-store-path: "client.p12"  # 客户端证书路径（用于双向认证）
  key-store-password: "xiang1"  # 客户端证书密码
  key-store-type: "PKCS12"  # 客户端证书类型
  protocols:  # 支持的SSL/TLS协议版本
    - "TLSv1.2"
    - "TLSv1.3"
  cipher-suites: []  # 密码套件（空数组表示使用默认）
  verify-hostname: false  # 是否验证服务器主机名
  handshake-timeout-seconds: 30  # SSL握手超时时间（秒）

# 在线数据源配置
online-data-sources:
  # 中国IP段数据源
  china-ip-ranges:
    - "https://raw.githubusercontent.com/mayaxcn/china-ip-list/master/chnroute.txt"
    - "https://raw.githubusercontent.com/17mon/china_ip_list/master/china_ip_list.txt"
