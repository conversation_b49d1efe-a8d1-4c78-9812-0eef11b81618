package com.proxy.client.ssl;

import com.proxy.client.config.ProxyClientConfigManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocket;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.Socket;
import java.security.cert.X509Certificate;

/**
 * 客户端SSL功能测试类
 */
public class ClientSslTest {
    private static final Logger logger = LoggerFactory.getLogger(ClientSslTest.class);
    
    private ClientSslContextManager sslContextManager;
    
    @BeforeEach
    void setUp() {
        sslContextManager = ClientSslContextManager.getInstance();
    }
    
    @Test
    void testClientSslContextCreation() {
        logger.info("测试客户端SSL上下文创建...");
        
        try {
            // 测试SSL上下文管理器初始化
            boolean sslEnabled = sslContextManager.isSslEnabled();
            logger.info("客户端SSL启用状态: {}", sslEnabled);
            
            if (sslEnabled) {
                // 尝试获取SSL上下文
                io.netty.handler.ssl.SslContext sslContext = sslContextManager.getClientSslContext();
                if (sslContext != null) {
                    logger.info("客户端SSL上下文创建成功");
                } else {
                    logger.warn("客户端SSL上下文为null");
                }
            }
            
            // 显示配置摘要
            logger.info("客户端SSL配置摘要: {}", sslContextManager.getSslConfigSummary());
            
        } catch (Exception e) {
            logger.error("客户端SSL上下文创建测试失败", e);
        }
    }
    
    @Test
    void testClientSslConfiguration() {
        logger.info("测试客户端SSL配置...");
        
        try {
            ProxyClientConfigManager configManager = ProxyClientConfigManager.getInstance();
            
            logger.info("客户端SSL配置详情:");
            logger.info("  启用: {}", configManager.isSslEnabled());
            logger.info("  信任所有证书: {}", configManager.isTrustAll());
            logger.info("  信任库路径: {}", configManager.getTrustStorePath());
            logger.info("  信任库类型: {}", configManager.getTrustStoreType());
            logger.info("  客户端证书路径: {}", configManager.getKeyStorePath());
            logger.info("  协议: {}", String.join(", ", configManager.getSslProtocols()));
            logger.info("  验证主机名: {}", configManager.isVerifyHostname());
            logger.info("  握手超时: {} 秒", configManager.getSslHandshakeTimeoutSeconds());
            
        } catch (Exception e) {
            logger.error("客户端SSL配置测试失败", e);
        }
    }
    
    /**
     * 测试SSL连接到代理服务器（需要服务器运行并启用SSL）
     * 这个测试需要手动运行，因为需要服务器实际启动
     */
    void testSslConnectionToProxyServer() {
        logger.info("测试SSL连接到代理服务器...");
        
        String host = "localhost";
        int port = 8888;
        
        try {
            // 创建信任所有证书的TrustManager（仅用于测试）
            TrustManager[] trustAllCerts = new TrustManager[] {
                ClientSslContextManager.createTrustAllManager()
            };
            
            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, trustAllCerts, new java.security.SecureRandom());
            
            SSLSocketFactory factory = sslContext.getSocketFactory();
            
            try (SSLSocket sslSocket = (SSLSocket) factory.createSocket(host, port)) {
                logger.info("SSL连接建立成功: {}:{}", host, port);
                
                // 获取SSL会话信息
                var session = sslSocket.getSession();
                logger.info("SSL协议: {}", session.getProtocol());
                logger.info("密码套件: {}", session.getCipherSuite());
                logger.info("对等主机: {}", session.getPeerHost());
                
                // 简单的数据交换测试
                OutputStream out = sslSocket.getOutputStream();
                InputStream in = sslSocket.getInputStream();
                
                // 发送测试数据
                String testMessage = "Client SSL Test Message";
                out.write(testMessage.getBytes());
                out.flush();
                
                logger.info("客户端SSL连接测试完成");
                
            }
            
        } catch (Exception e) {
            logger.error("客户端SSL连接测试失败", e);
        }
    }
    
    /**
     * 测试普通TCP连接（用于对比）
     */
    void testPlainConnectionToProxyServer() {
        logger.info("测试普通TCP连接到代理服务器...");
        
        String host = "localhost";
        int port = 8888;
        
        try (Socket socket = new Socket(host, port)) {
            logger.info("普通TCP连接建立成功: {}:{}", host, port);
            
            OutputStream out = socket.getOutputStream();
            InputStream in = socket.getInputStream();
            
            // 发送测试数据
            String testMessage = "Client Plain Test Message";
            out.write(testMessage.getBytes());
            out.flush();
            
            logger.info("客户端普通TCP连接测试完成");
            
        } catch (IOException e) {
            logger.error("客户端普通TCP连接测试失败", e);
        }
    }
    
    /**
     * 测试SSL配置验证
     */
    @Test
    void testSslConfigValidation() {
        logger.info("测试SSL配置验证...");
        
        try {
            ProxyClientConfigManager configManager = ProxyClientConfigManager.getInstance();
            
            // 验证基本配置
            boolean sslEnabled = configManager.isSslEnabled();
            logger.info("SSL启用: {}", sslEnabled);
            
            if (sslEnabled) {
                // 验证协议配置
                String[] protocols = configManager.getSslProtocols();
                logger.info("支持的协议: {}", String.join(", ", protocols));
                
                // 验证安全配置
                boolean trustAll = configManager.isTrustAll();
                boolean verifyHostname = configManager.isVerifyHostname();
                
                if (trustAll) {
                    logger.warn("警告: 启用了信任所有证书，这在生产环境中不安全");
                }
                
                if (!verifyHostname) {
                    logger.warn("警告: 禁用了主机名验证，这在生产环境中不安全");
                }
                
                // 验证超时配置
                int handshakeTimeout = configManager.getSslHandshakeTimeoutSeconds();
                if (handshakeTimeout < 5 || handshakeTimeout > 120) {
                    logger.warn("警告: SSL握手超时时间可能不合适: {} 秒", handshakeTimeout);
                }
            }
            
        } catch (Exception e) {
            logger.error("SSL配置验证失败", e);
        }
    }
    
    /**
     * 手动测试方法
     * 运行这个方法来进行完整的客户端SSL测试
     */
    public static void main(String[] args) {
        ClientSslTest test = new ClientSslTest();
        test.setUp();
        
        logger.info("=== 客户端SSL功能测试开始 ===");
        
        // 测试SSL上下文创建
        test.testClientSslContextCreation();
        
        // 测试SSL配置
        test.testClientSslConfiguration();
        
        // 测试SSL配置验证
        test.testSslConfigValidation();
        
        // 如果需要测试实际连接，请先启动服务器并启用SSL
        // test.testSslConnectionToProxyServer();
        // test.testPlainConnectionToProxyServer();
        
        logger.info("=== 客户端SSL功能测试完成 ===");
    }
}
