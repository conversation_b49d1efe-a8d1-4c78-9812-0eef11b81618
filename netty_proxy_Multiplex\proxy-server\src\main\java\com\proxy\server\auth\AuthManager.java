package com.proxy.server.auth;

import io.netty.channel.Channel;
import io.netty.util.AttributeKey;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 认证管理器
 * 管理客户端连接的认证状态
 */
public class AuthManager {
    private static final Logger logger = LoggerFactory.getLogger(AuthManager.class);
    
    // 单例实例
    private static final AuthManager INSTANCE = new AuthManager();
    
    // 认证状态属性键
    public static final AttributeKey<Boolean> AUTH_STATUS = AttributeKey.valueOf("auth_status");
    public static final AttributeKey<String> AUTH_USERNAME = AttributeKey.valueOf("auth_username");
    public static final AttributeKey<Long> AUTH_TIME = AttributeKey.valueOf("auth_time");
    
    // 等待认证的连接
    private final ConcurrentHashMap<String, Long> pendingAuth = new ConcurrentHashMap<>();
    
    // 定时清理任务
    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
        Thread t = new Thread(r, "AuthManager-Cleaner");
        t.setDaemon(true);
        return t;
    });
    
    private AuthManager() {
        // 启动定时清理任务
        scheduler.scheduleAtFixedRate(this::cleanupExpiredAuth, 
            30, 30, TimeUnit.SECONDS);
    }
    
    public static AuthManager getInstance() {
        return INSTANCE;
    }
    
    /**
     * 检查连接是否已认证
     */
    public boolean isAuthenticated(Channel channel) {
        if (!AuthConfig.isAuthEnabled()) {
            return true; // 未启用认证时，总是返回已认证
        }
        
        Boolean authStatus = channel.attr(AUTH_STATUS).get();
        return authStatus != null && authStatus;
    }
    
    /**
     * 检查连接是否需要认证
     */
    public boolean requiresAuth(Channel channel) {
        if (!AuthConfig.isAuthEnabled()) {
            return false; // 未启用认证时，不需要认证
        }
        
        return !isAuthenticated(channel);
    }
    
    /**
     * 标记连接开始认证过程
     */
    public void startAuthProcess(Channel channel) {
        if (!AuthConfig.isAuthEnabled()) {
            return;
        }
        
        String channelId = channel.id().asShortText();
        pendingAuth.put(channelId, System.currentTimeMillis());
        
        logger.debug("连接 {} 开始认证过程", channelId);
    }
    
    /**
     * 处理认证请求
     */
    public boolean authenticate(Channel channel, String username, String password) {
        if (!AuthConfig.isAuthEnabled()) {
            return true; // 未启用认证时，总是成功
        }
        
        String channelId = channel.id().asShortText();
        
        // 验证凭据
        boolean success = AuthConfig.validateCredentials(username, password);
        
        if (success) {
            // 认证成功
            channel.attr(AUTH_STATUS).set(true);
            channel.attr(AUTH_USERNAME).set(username);
            channel.attr(AUTH_TIME).set(System.currentTimeMillis());
            pendingAuth.remove(channelId);
            
            logger.info("连接 {} 认证成功，用户: {}", channelId, username);
        } else {
            // 认证失败
            logger.warn("连接 {} 认证失败，用户: {}", channelId, username);
        }
        
        return success;
    }
    
    /**
     * 清理连接的认证状态
     */
    public void clearAuth(Channel channel) {
        String channelId = channel.id().asShortText();
        pendingAuth.remove(channelId);
        
        channel.attr(AUTH_STATUS).set(null);
        channel.attr(AUTH_USERNAME).set(null);
        channel.attr(AUTH_TIME).set(null);
        
        logger.debug("清理连接 {} 的认证状态", channelId);
    }
    
    /**
     * 检查认证是否超时
     */
    public boolean isAuthTimeout(Channel channel) {
        if (!AuthConfig.isAuthEnabled()) {
            return false;
        }
        
        String channelId = channel.id().asShortText();
        Long startTime = pendingAuth.get(channelId);
        
        if (startTime == null) {
            return false; // 没有在等待认证
        }
        
        long elapsed = System.currentTimeMillis() - startTime;
        return elapsed > AuthConfig.getAuthTimeoutSeconds() * 1000L;
    }
    
    /**
     * 获取认证统计信息
     */
    public String getAuthStats() {
        return String.format("等待认证连接数: %d, 认证超时时间: %ds", 
            pendingAuth.size(), AuthConfig.getAuthTimeoutSeconds());
    }
    
    /**
     * 清理过期的认证等待
     */
    private void cleanupExpiredAuth() {
        long now = System.currentTimeMillis();
        long timeoutMs = AuthConfig.getAuthTimeoutSeconds() * 1000L;
        
        pendingAuth.entrySet().removeIf(entry -> {
            boolean expired = (now - entry.getValue()) > timeoutMs;
            if (expired) {
                logger.debug("清理过期的认证等待: {}", entry.getKey());
            }
            return expired;
        });
    }
    
    /**
     * 关闭认证管理器
     */
    public void shutdown() {
        scheduler.shutdown();
        pendingAuth.clear();
        logger.info("认证管理器已关闭");
    }
}
