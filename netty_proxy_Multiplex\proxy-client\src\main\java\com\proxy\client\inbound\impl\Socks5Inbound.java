package com.proxy.client.inbound.impl;

import com.proxy.client.connection.ConnectionManager;
import com.proxy.client.filter.AddressFilter;
import com.proxy.client.config.ProxyClientConfigManager;
import com.proxy.client.handler.MultiplexSocks5Handler;
import com.proxy.client.inbound.AbstractProxyInbound;
import io.netty.channel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * SOCKS5代理接入组件
 * 支持SOCKS5代理协议
 */
public class Socks5Inbound extends AbstractProxyInbound {
    private static final Logger logger = LoggerFactory.getLogger(Socks5Inbound.class);
    
    public Socks5Inbound(int port, ConnectionManager connectionManager,
                        AddressFilter addressFilter, ProxyClientConfigManager configManager) {
        super("SOCKS5-Proxy", ProxyProtocol.SOCKS5, port, connectionManager, addressFilter, configManager);
    }
    
    @Override
    protected ChannelInitializer<Channel> createChannelInitializer() {
        return new ChannelInitializer<Channel>() {
            @Override
            protected void initChannel(Channel ch) throws Exception {
                ChannelPipeline pipeline = ch.pipeline();
                
                // 添加连接统计处理器
                pipeline.addLast("connection-counter", new ConnectionCounterHandler());
                
                // 添加SOCKS5处理器
                pipeline.addLast("socks5-handler", new MultiplexSocks5Handler());
            }
        };
    }
    
    /**
     * 连接计数处理器
     */
    private class ConnectionCounterHandler extends ChannelInboundHandlerAdapter {
        @Override
        public void channelActive(ChannelHandlerContext ctx) throws Exception {
            onConnectionEstablished();
            logger.debug("SOCKS5客户端连接建立: {}", ctx.channel().remoteAddress());
            super.channelActive(ctx);
        }
        
        @Override
        public void channelInactive(ChannelHandlerContext ctx) throws Exception {
            onConnectionClosed();
            logger.debug("SOCKS5客户端连接关闭: {}", ctx.channel().remoteAddress());
            super.channelInactive(ctx);
        }
    }
}
