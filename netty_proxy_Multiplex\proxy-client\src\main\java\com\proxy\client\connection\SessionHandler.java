package com.proxy.client.connection;

/**
 * 会话处理器接口
 * 处理多路复用连接中的单个会话事件
 */
public interface SessionHandler {
    
    /**
     * 连接响应回调
     * @param success 连接是否成功
     * @param serverSessionId 服务器分配的sessionId（仅在使用V2协议时有效）
     */
    void onConnectResponse(boolean success, int serverSessionId);
    
    /**
     * 数据接收回调
     * @param data 接收到的数据
     */
    void onData(byte[] data);
    
    /**
     * UDP数据接收回调
     * @param data 接收到的UDP数据
     */
    default void onUdpData(byte[] data) {
        // 默认实现：委托给onData方法以保持向后兼容性
        onData(data);
    }
    
    /**
     * 连接关闭回调
     */
    void onClose();
    
    /**
     * 关闭会话
     */
    void close();
}
