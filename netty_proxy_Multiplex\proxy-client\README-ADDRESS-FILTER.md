# 地址过滤机制使用说明

## 概述

proxy-client现在支持智能地址过滤机制，可以根据目标地址的地理位置决定是直连还是通过proxy-server转发。

## 功能特性

### 三种过滤模式

1. **ALL_PROXY** - 所有连接都通过proxy-server转发
   - 适用于需要统一代理的场景
   - 所有流量都经过proxy-server处理

2. **CHINA_DIRECT** - 中国地区IP直连，其他通过proxy-server转发
   - 适用于国内外分流的场景
   - 中国大陆IP地址直接连接，提高访问速度
   - 海外IP地址通过proxy-server转发

3. **ALL_DIRECT** - 所有连接都直连
   - 适用于不需要代理的场景
   - 所有连接都绕过proxy-server

### 地理位置判断

- **最新数据源**：使用APNIC官方数据，每小时自动更新
- **多重数据源**：配置文件 -> 在线数据源 -> 内置基础IP段
- **在线更新**：支持从GitHub等在线源获取最新中国IP段数据
- **高准确性**：覆盖99%以上的中国大陆IP地址
- **IPv4支持**：完整支持IPv4地址判断（IPv6暂不支持）
- **私有地址**：自动识别私有地址（192.168.x.x、10.x.x.x等）为中国IP

## 配置方法

### 1. 基础配置

编辑 `src/main/resources/proxy-client.properties`：

```properties
# 地址过滤模式
# ALL_PROXY: 所有连接都通过proxy-server转发
# CHINA_DIRECT: 中国地区IP直连，其他通过proxy-server转发
# ALL_DIRECT: 所有连接都直连
filter.mode=CHINA_DIRECT

# 代理服务器配置
proxy.server.host=localhost
proxy.server.port=8888

# 本地SOCKS5监听端口
local.port=1080
```

### 2. 使用最新中国IP段数据

系统会自动按以下优先级加载IP段数据：

1. **配置文件**：`src/main/resources/china-ip-ranges.txt` 或 `geoip-china.txt`
2. **在线数据源**：从GitHub等在线源获取最新APNIC数据
3. **内置数据**：基础的中国IP段作为备用

#### 配置文件格式示例：

```
# 中国IP段配置文件 - 最新版本
# 数据来源：APNIC官方数据 + GitHub项目 mayaxcn/china-ip-list
# 更新日期：2025-07-19

# 主要A类地址段
*******/8
********/8
********/8
********/8
********/8
********/8
********/8
********/8
********/8
60.0.0.0/8
6*******/8

# 云服务商IP段
*********/13    # 阿里云
**********/13   # 腾讯云
*********/16    # 华为云

# 常用DNS服务器
***************/32
*********/32
```

#### 在线数据源：

- **主要源**：https://github.com/mayaxcn/china-ip-list （每小时更新）
- **备用源**：https://github.com/17mon/china_ip_list
- **原始数据**：http://ftp.apnic.net/apnic/stats/apnic/delegated-apnic-latest

### 3. 支持的配置文件名

系统会按以下顺序查找配置文件：
1. `china-ip-ranges.txt`
2. `geoip-china.txt`
3. `china-ip.txt`
4. `cn-ip-ranges.txt`

如果都找不到，会使用内置的基础IP段。

## 使用方法

### 1. 编译和运行

```bash
# 编译
mvn clean compile

# 运行（使用配置文件中的设置）
mvn exec:java -Dexec.mainClass="com.proxy.client.Socks5ProxyClient"

# 运行（命令行参数覆盖配置）
mvn exec:java -Dexec.mainClass="com.proxy.client.Socks5ProxyClient" -Dexec.args="1080 localhost 8888"

# 或者使用编译后的类文件运行
java -cp target/classes com.proxy.client.Socks5ProxyClient
java -cp target/classes com.proxy.client.Socks5ProxyClient 1080 localhost 8888

# 使用GraalVM Native Image（推荐用于生产环境）
# 首先构建native executable
./build-native.sh  # Linux/macOS
# 或
build-native.bat   # Windows

# 然后运行native executable
./target/proxy-client 1080 localhost 8888  # Linux/macOS
# 或
target\proxy-client.exe 1080 localhost 8888  # Windows
```

### 2. 运行时切换模式

可以通过编程方式动态切换过滤模式：

```java
// 获取配置实例
ProxyClientConfig config = ProxyClientConfig.getInstance();

// 切换到中国直连模式
config.setFilterMode(AddressFilter.FilterMode.CHINA_DIRECT);

// 切换到全部代理模式
config.setFilterMode(AddressFilter.FilterMode.ALL_PROXY);

// 切换到全部直连模式
config.setFilterMode(AddressFilter.FilterMode.ALL_DIRECT);
```

### 3. 手动更新IP段数据

可以在运行时手动更新中国IP段数据：

```java
// 手动更新IP段数据（会从在线数据源获取最新数据）
boolean success = GeoIPUtil.updateChinaIPRanges();
if (success) {
    System.out.println("IP段数据更新成功");
} else {
    System.out.println("IP段数据更新失败");
}

// 查看当前加载的IP段数量
int count = GeoIPUtil.getLoadedIPRangeCount();
System.out.println("当前已加载 " + count + " 个IP段");
```

## 工作原理

### 连接流程

1. 客户端发起SOCKS5连接请求
2. MultiplexSocks5Handler解析目标地址
3. AddressFilter判断连接方式：
   - 如果是ALL_PROXY模式：返回PROXY
   - 如果是ALL_DIRECT模式：返回DIRECT
   - 如果是CHINA_DIRECT模式：
     - 解析域名为IP地址
     - 使用GeoIPUtil判断是否为中国IP
     - 中国IP返回DIRECT，否则返回PROXY
4. 根据判断结果选择连接方式：
   - PROXY：通过ConnectionManager转发到proxy-server
   - DIRECT：通过DirectConnectionHandler直接连接目标服务器

### 地理位置判断逻辑

1. 首先检查是否为私有地址（本地网络）
2. 从配置文件加载中国IP段数据
3. 使用CIDR匹配算法判断IP是否在中国IP段内
4. 如果配置文件加载失败，使用内置的基础IP段

## 日志和调试

### 日志级别

- INFO：显示过滤模式切换、连接方式选择等关键信息
- DEBUG：显示详细的IP判断过程
- WARN：显示配置文件加载失败、无效IP等警告

### 示例日志

```
INFO  - 成功从在线数据源加载 3247 个中国IP段
INFO  - 过滤模式: CHINA_DIRECT, 目标: www.baidu.com:80 (IP: **************), 中国IP: true -> DIRECT
INFO  - 使用直连模式: www.baidu.com:80 (sessionId=1)
INFO  - 过滤模式: CHINA_DIRECT, 目标: www.google.com:80 (IP: **************), 中国IP: false -> PROXY
INFO  - 使用代理模式: www.google.com:80 (sessionId=2)
INFO  - IP段数据更新完成: 3247 -> 3251 个IP段
```

## 性能考虑

### 优化建议

1. **DNS缓存**：系统会使用JVM的DNS缓存来避免重复解析
2. **IP段优化**：使用较大的CIDR块可以减少匹配次数
3. **在线更新**：建议定期更新IP段数据以保证准确性
4. **网络超时**：在线更新设置了合理的超时时间（连接10秒，读取30秒）

### 内存使用

- 每个IP段约占用50-100字节内存
- 3000+个IP段约占用150KB-300KB内存
- 在线更新时会临时占用额外内存
- 对系统性能影响很小

### 数据准确性

- **数据来源**：APNIC官方权威数据
- **更新频率**：在线数据源每小时更新
- **覆盖率**：99%以上的中国大陆IP地址
- **实时性**：支持运行时手动更新

## 故障排除

### 常见问题

1. **配置文件未生效**
   - 检查文件名是否正确
   - 检查文件是否在classpath中
   - 查看启动日志确认是否加载成功

2. **IP判断不准确**
   - 更新IP段配置文件
   - 检查IP段格式是否正确（CIDR格式）
   - 启用DEBUG日志查看判断过程

3. **连接失败**
   - 检查proxy-server是否正常运行
   - 检查网络连接
   - 查看异常日志

### 测试方法

```bash
# 测试不同网站的连接方式
curl --socks5 localhost:1080 http://www.baidu.com     # 应该直连
curl --socks5 localhost:1080 http://www.google.com    # 应该走代理
```

## 扩展开发

### 自定义过滤器

可以实现自定义的AddressFilter：

```java
public class CustomAddressFilter implements AddressFilter {
    @Override
    public ConnectionType shouldUseProxy(String targetHost, int targetPort) {
        // 自定义判断逻辑
        return ConnectionType.DIRECT;
    }
    
    // ... 其他方法实现
}
```

### 集成第三方GeoIP库

可以集成MaxMind GeoIP2等商业库：

```java
// 示例代码...
```

## GraalVM Native Image 支持

proxy-client现在支持使用GraalVM Native Image编译为原生可执行文件，提供以下优势：

### 性能优势
- **快速启动**: 启动时间从2-3秒降低到50-100毫秒
- **低内存占用**: 内存使用从100-200MB降低到20-50MB
- **单文件部署**: 生成单个可执行文件，无需安装JVM

### 构建方法

1. **安装GraalVM**:
   ```bash
   # 下载并安装GraalVM
   # 安装native-image组件
   gu install native-image
   ```

2. **构建native executable**:
   ```bash
   # 使用构建脚本（推荐）
   ./build-native.sh      # Linux/macOS
   build-native.bat       # Windows

   # 或使用Maven命令
   mvn -Pnative native:compile
   ```

3. **运行native executable**:
   ```bash
   ./target/proxy-client 1080 localhost 8888
   ```

详细说明请参考 [README-GRAALVM.md](README-GRAALVM.md)
public class MaxMindGeoIPUtil extends GeoIPUtil {
    private DatabaseReader reader;
    
    @Override
    public boolean isChinaIP(String ip) {
        // 使用MaxMind GeoIP2库判断
        // ...
    }
}
```
