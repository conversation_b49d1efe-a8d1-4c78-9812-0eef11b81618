package com.proxy.server.pool;

import com.proxy.server.config.ConnectionPoolConfig;
import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.PooledByteBufAllocator;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.util.concurrent.DefaultThreadFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 连接池实现
 * 管理后端服务器连接的复用
 */
public class ConnectionPool {
    private static final Logger logger = LoggerFactory.getLogger(ConnectionPool.class);
    private static final ConnectionPool INSTANCE = new ConnectionPool();

    // 连接池，key为hostKey (host:port)，value为该主机的连接队列
    private final Map<String, Queue<PooledConnection>> connectionPool = new ConcurrentHashMap<>();

    // 定时清理任务
    private final ScheduledExecutorService cleanupExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
        Thread t = new Thread(r, "ConnectionPool-Cleanup");
        t.setDaemon(true);
        return t;
    });

    private volatile boolean started = false;

    private ConnectionPool() {
    }
    
    public static ConnectionPool getInstance() {
        return INSTANCE;
    }

    /**
     * 启动连接池（包括定时清理任务）
     */
    public synchronized void start() {
        if (!started) {
            started = true;
            // 启动定时清理任务
            cleanupExecutor.scheduleAtFixedRate(
                this::cleanupExpiredConnections,
                ConnectionPoolConfig.POOL_CLEANUP_INTERVAL,
                ConnectionPoolConfig.POOL_CLEANUP_INTERVAL,
                TimeUnit.MILLISECONDS
            );
            logger.info("连接池已启动，清理间隔: {}ms", ConnectionPoolConfig.POOL_CLEANUP_INTERVAL);
        }
    }
    
    /**
     * 获取连接
     * @param hostKey 主机标识 (host:port)
     * @return 可用的连接，如果没有则返回null
     */
    public synchronized Channel getConnection(String hostKey) {
        if (!ConnectionPoolConfig.ENABLE_CONNECTION_POOL) {
            return null;
        }

        if (hostKey == null || hostKey.trim().isEmpty()) {
            logger.warn("无效的主机键值: {}", hostKey);
            return null;
        }

        Queue<PooledConnection> connections = connectionPool.get(hostKey);
        if (connections == null || connections.isEmpty()) {
            logger.debug("没有可用连接，需要创建新连接: {}", hostKey);
            return null;
        }

        // 使用FIFO策略获取连接，并进行严格的状态验证
        Iterator<PooledConnection> iterator = connections.iterator();
        while (iterator.hasNext()) {
            PooledConnection conn = iterator.next();
            Channel channel = conn.getChannelWithoutUpdate();

            // 检查连接是否超时
            if (isConnectionExpired(conn)) {
                iterator.remove();
                if (channel != null && channel.isActive()) {
                    logger.debug("连接已超时，关闭连接: {}，连接ID: {}", hostKey, channel.id());
                    channel.close();
                }
                continue;
            }

            // 严格检查连接是否可用
            if (isConnectionUsable(channel)) {
                // 从队列中移除选中的连接
                iterator.remove();

                // 清理连接状态并准备重用
                if (prepareConnectionForReuse(channel, hostKey)) {
                    // 更新使用时间并返回连接
                    Channel activeChannel = conn.getChannel();
                    logger.debug("从连接池获取连接: {}，连接ID: {}，剩余连接数: {}",
                        hostKey, activeChannel.id(), connections.size());
                    return activeChannel;
                } else {
                    // 准备失败，继续寻找下一个连接
                    continue;
                }
            } else {
                // 连接已失效，从队列中移除并关闭
                iterator.remove();
                if (channel != null) {
                    logger.debug("连接已失效，关闭连接: {}，连接ID: {} (active:{}, writable:{}, open:{})",
                        hostKey, channel.id(), channel.isActive(), channel.isWritable(), channel.isOpen());
                    safeCloseChannel(channel);
                }
            }
        }

        logger.debug("没有可用的有效连接，需要创建新连接: {}", hostKey);
        return null;
    }

    /**
     * 检查连接是否可用
     */
    private boolean isConnectionUsable(Channel channel) {
        if (channel == null) {
            return false;
        }

        // 基本状态检查
        if (!channel.isActive() || !channel.isWritable() || !channel.isOpen()) {
            return false;
        }

        // 检查连接是否正在被其他线程使用
        // 这里可以添加更多的状态检查逻辑

        return true;
    }

    /**
     * 检查连接是否已过期
     */
    private boolean isConnectionExpired(PooledConnection conn) {
        long currentTime = System.currentTimeMillis();
        long idleTime = currentTime - conn.getLastUsedTime();
        return idleTime > ConnectionPoolConfig.CONNECTION_IDLE_TIMEOUT;
    }

    /**
     * 准备连接以供重用
     */
    private boolean prepareConnectionForReuse(Channel channel, String hostKey) {
        try {
            // 清理所有可能存在的旧处理器
            ChannelPipeline pipeline = channel.pipeline();

            // 移除所有自定义处理器，保留基础的编解码器
            String[] handlersToRemove = {"multiplexBackendHandler", "httpHandler", "sslHandler"};
            for (String handlerName : handlersToRemove) {
                if (pipeline.get(handlerName) != null) {
                    pipeline.remove(handlerName);
                    logger.debug("清理连接池连接的处理器: {}", handlerName);
                }
            }

            // 验证连接在清理后仍然可用
            if (!isConnectionUsable(channel)) {
                logger.warn("连接在清理后变为不可用: {}", hostKey);
                safeCloseChannel(channel);
                return false;
            }

            return true;
        } catch (Exception e) {
            logger.warn("准备连接重用时发生异常: {}，连接ID: {}", hostKey, channel.id(), e);
            safeCloseChannel(channel);
            return false;
        }
    }

    /**
     * 安全关闭连接
     */
    private void safeCloseChannel(Channel channel) {
        if (channel != null) {
            try {
                channel.close();
            } catch (Exception e) {
                logger.debug("关闭连接时出现异常: {}", e.getMessage());
            }
        }
    }
    
    /**
     * 归还连接到连接池
     * @param hostKey 主机标识 (host:port)
     * @param channel 要归还的连接
     */
    public void returnConnection(String hostKey, Channel channel) {
        // 快速检查，无需同步
        if (!ConnectionPoolConfig.ENABLE_CONNECTION_POOL) {
            logger.debug("连接池未启用，关闭连接: {}，连接ID: {}",
                hostKey, channel != null ? channel.id() : "null");
            safeCloseChannel(channel);
            return;
        }

        if (channel == null) {
            logger.warn("尝试归还空连接到连接池: {}", hostKey);
            return;
        }

        if (hostKey == null || hostKey.trim().isEmpty()) {
            logger.warn("主机键值无效，关闭连接: 连接ID: {}", channel.id());
            safeCloseChannel(channel);
            return;
        }

        // 在同步块外进行连接适用性检查和清理
        if (!isConnectionSuitableForReturn(channel, hostKey)) {
            return;
        }

        // 在同步块外进行连接清理，减少锁持有时间
        if (!cleanupConnectionForReturn(channel, hostKey)) {
            logger.debug("连接清理失败，关闭连接: {}，连接ID: {}", hostKey, channel.id());
            safeCloseChannel(channel);
            return;
        }

        // 只在关键操作时同步
        synchronized (this) {
            try {
                // 获取或创建该主机的连接队列
                Queue<PooledConnection> connections = connectionPool.computeIfAbsent(
                    hostKey, k -> new ConcurrentLinkedQueue<>()
                );

                // 检查连接数是否超过限制（在同步块内确保原子性）
                int maxConnections = ConnectionPoolConfig.MAX_CONNECTIONS_PER_HOST;
                if (connections.size() >= maxConnections) {
                    // 连接池已满，关闭连接
                    logger.debug("连接池已满，关闭连接: {}，连接ID: {}", hostKey, channel.id());
                    safeCloseChannel(channel);
                    return;
                }

                // 添加连接到池中
                PooledConnection pooledConnection = new PooledConnection(channel);
                connections.offer(pooledConnection);
                logger.debug("连接归还到连接池: {}，连接ID: {}，当前池中连接数: {}",
                    hostKey, channel.id(), connections.size());

            } catch (Exception e) {
                logger.error("归还连接到连接池时发生异常: {}，连接ID: {}",
                    hostKey, channel.id(), e);
                safeCloseChannel(channel);
            }
        }
    }

    /**
     * 检查连接是否适合归还到连接池
     */
    private boolean isConnectionSuitableForReturn(Channel channel, String hostKey) {
        if (!channel.isActive()) {
            logger.debug("连接已失效，无法归还到连接池: {}，连接ID: {}", hostKey, channel.id());
            return false;
        }

        // 基本状态检查
        if (!channel.isWritable() || !channel.isOpen()) {
            logger.debug("连接状态不佳，无法归还: {}，连接ID: {} (active:{}, writable:{}, open:{})",
                hostKey, channel.id(), channel.isActive(), channel.isWritable(), channel.isOpen());
            safeCloseChannel(channel);
            return false;
        }

        // 检查连接是否有未处理的数据
        if (channel.pipeline().context("multiplexBackendHandler") != null) {
            // 如果还有处理器在处理数据，说明连接可能还在使用中
            logger.debug("连接仍在使用中，无法归还: {}，连接ID: {}", hostKey, channel.id());
            safeCloseChannel(channel);
            return false;
        }

        return true;
    }

    /**
     * 清理连接状态以准备归还
     */
    private boolean cleanupConnectionForReturn(Channel channel, String hostKey) {
        try {
            ChannelPipeline pipeline = channel.pipeline();

            // 移除所有可能影响重用的处理器
            String[] handlersToRemove = {"multiplexBackendHandler", "httpHandler", "sslHandler", "timeoutHandler"};
            for (String handlerName : handlersToRemove) {
                if (pipeline.get(handlerName) != null) {
                    pipeline.remove(handlerName);
                    logger.debug("清理归还连接的处理器: {} - {}", handlerName, hostKey);
                }
            }

            // 验证连接在清理后仍然可用
            if (!channel.isActive() || !channel.isWritable()) {
                logger.warn("连接在清理后变为不可用: {}", hostKey);
                return false;
            }

            return true;
        } catch (Exception e) {
            logger.warn("清理归还连接时发生异常: {}，连接ID: {}", hostKey, channel.id(), e);
            return false;
        }
    }
    
    /**
     * 添加新连接计数
     * @param hostKey 主机标识 (host:port)
     */
    public void addConnection(String hostKey) {
        // 简化实现，不再维护活跃连接计数
    }
    


    
    /**
     * 清理过期连接
     */
    private void cleanupExpiredConnections() {
        try {
            int totalCleaned = 0;
            for (Map.Entry<String, Queue<PooledConnection>> entry : connectionPool.entrySet()) {
                String hostKey = entry.getKey();
                Queue<PooledConnection> connections = entry.getValue();

                Iterator<PooledConnection> iterator = connections.iterator();
                int cleanedForHost = 0;

                while (iterator.hasNext()) {
                    PooledConnection conn = iterator.next();

                    // 检查连接是否过期或无效
                    if (isConnectionExpired(conn) || !isConnectionUsable(conn.getChannelWithoutUpdate())) {
                        iterator.remove();
                        Channel channel = conn.getChannelWithoutUpdate();
                        if (channel != null && channel.isActive()) {
                            safeCloseChannel(channel);
                        }
                        cleanedForHost++;
                        totalCleaned++;
                    }
                }

                if (cleanedForHost > 0) {
                    logger.debug("清理过期连接: {} - {} 个连接", hostKey, cleanedForHost);
                }
            }

            if (totalCleaned > 0) {
                logger.info("连接池清理完成，共清理 {} 个过期连接", totalCleaned);
            }
        } catch (Exception e) {
            logger.error("清理过期连接时发生异常", e);
        }
    }

    /**
     * 关闭连接池
     */
    public synchronized void shutdown() {
        started = false;

        // 关闭定时清理任务
        if (cleanupExecutor != null && !cleanupExecutor.isShutdown()) {
            cleanupExecutor.shutdown();
            try {
                if (!cleanupExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    cleanupExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                cleanupExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        // 关闭所有连接
        for (Queue<PooledConnection> connections : connectionPool.values()) {
            for (PooledConnection conn : connections) {
                Channel channel = conn.getChannelWithoutUpdate();
                if (channel != null && channel.isActive()) {
                    safeCloseChannel(channel);
                }
            }
        }

        // 清空连接池
        connectionPool.clear();

        logger.info("连接池已关闭");
    }
    

    
    /**
     * 获取连接池状态信息
     */
    public String getPoolStats() {
        StringBuilder stats = new StringBuilder();
        stats.append("连接池状态:\n");

        for (Map.Entry<String, Queue<PooledConnection>> entry : connectionPool.entrySet()) {
            String hostKey = entry.getKey();
            int pooledCount = entry.getValue().size();

            stats.append(String.format("  %s: 池中=%d\n", hostKey, pooledCount));
        }

        return stats.toString();
    }
    
    /**
     * 连接池中的连接包装类
     */
    private static class PooledConnection {
        private final Channel channel;
        private final long creationTime;
        private long lastUsedTime;

        public PooledConnection(Channel channel) {
            this.channel = channel;
            this.creationTime = System.currentTimeMillis();
            this.lastUsedTime = this.creationTime;
        }

        /**
         * 获取通道并更新最后使用时间
         * @return 通道对象
         */
        public Channel getChannel() {
            this.lastUsedTime = System.currentTimeMillis();
            return channel;
        }

        /**
         * 获取通道但不更新最后使用时间
         * @return 通道对象
         */
        public Channel getChannelWithoutUpdate() {
            return channel;
        }

        /**
         * 获取最后使用时间
         * @return 最后使用时间戳
         */
        public long getLastUsedTime() {
            return lastUsedTime;
        }

        /**
         * 获取创建时间
         * @return 创建时间戳
         */
        public long getCreationTime() {
            return creationTime;
        }

        /**
         * 检查连接是否空闲超时
         * @return 如果超时返回true
         */
        public boolean isIdleTimeout() {
            long currentTime = System.currentTimeMillis();
            long idleTime = currentTime - lastUsedTime;
            return idleTime > ConnectionPoolConfig.CONNECTION_IDLE_TIMEOUT;
        }
    }
}