package com.proxy.client.inbound.impl;

import com.proxy.client.connection.ConnectionManager;
import com.proxy.client.connection.DirectConnectionHandler;
import com.proxy.client.connection.SessionHandler;
import com.proxy.client.filter.AddressFilter;
import com.proxy.client.config.ProxyClientConfigManager;
import com.proxy.client.handler.MultiplexSessionHandler;
import com.proxy.client.inbound.AbstractProxyInbound;
import io.netty.buffer.ByteBuf;
import io.netty.channel.*;
import io.netty.handler.codec.http.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * HTTP代理接入组件
 * 支持HTTP CONNECT代理协议
 */
public class HttpInbound extends AbstractProxyInbound {
    private static final Logger logger = LoggerFactory.getLogger(HttpInbound.class);
    
    public HttpInbound(int port, ConnectionManager connectionManager,
                      AddressFilter addressFilter, ProxyClientConfigManager configManager) {
        super("HTTP-Proxy", ProxyProtocol.HTTP, port, connectionManager, addressFilter, configManager);
    }
    
    @Override
    protected ChannelInitializer<Channel> createChannelInitializer() {
        return new ChannelInitializer<Channel>() {
            @Override
            protected void initChannel(Channel ch) throws Exception {
                ChannelPipeline pipeline = ch.pipeline();
                
                // HTTP编解码器
                pipeline.addLast("http-codec", new HttpServerCodec());
                pipeline.addLast("http-aggregator", new HttpObjectAggregator(8192));
                
                // HTTP代理处理器
                pipeline.addLast("http-proxy-handler", new HttpProxyHandler());
            }
        };
    }
    
    /**
     * HTTP代理处理器
     */
    private class HttpProxyHandler extends SimpleChannelInboundHandler<FullHttpRequest> {
        private volatile boolean connected = false;
        
        @Override
        public void channelActive(ChannelHandlerContext ctx) throws Exception {
            onConnectionEstablished();
            logger.debug("HTTP客户端连接建立: {}", ctx.channel().remoteAddress());
            super.channelActive(ctx);
        }
        
        @Override
        public void channelInactive(ChannelHandlerContext ctx) throws Exception {
            onConnectionClosed();
            logger.debug("HTTP客户端连接关闭: {}", ctx.channel().remoteAddress());
            super.channelInactive(ctx);
        }
        
        @Override
        protected void channelRead0(ChannelHandlerContext ctx, FullHttpRequest request) throws Exception {
            if (request.method() == HttpMethod.CONNECT) {
                handleConnectRequest(ctx, request);
            } else {
                // 不支持的HTTP方法
                sendErrorResponse(ctx, HttpResponseStatus.METHOD_NOT_ALLOWED, 
                    "Only CONNECT method is supported");
            }
        }
        
        /**
         * 处理HTTP CONNECT请求
         */
        private void handleConnectRequest(ChannelHandlerContext ctx, FullHttpRequest request) {
            String uri = request.uri();
            logger.debug("收到HTTP CONNECT请求: {}", uri);
            
            try {
                // 解析目标地址
                String[] hostPort = parseHostPort(uri);
                String targetHost = hostPort[0];
                int targetPort = Integer.parseInt(hostPort[1]);
                
                logger.info("HTTP CONNECT请求: {}:{}", targetHost, targetPort);
                
                // 检查地址过滤
                AddressFilter.ConnectionType connectionType = addressFilter.shouldUseProxy(targetHost, targetPort);

                if (connectionType == AddressFilter.ConnectionType.DIRECT) {
                    // 直连
                    logger.debug("HTTP CONNECT直连: {}:{}", targetHost, targetPort);
                    handleDirectConnect(ctx, targetHost, targetPort);
                } else {
                    // 通过代理服务器
                    logger.debug("HTTP CONNECT代理: {}:{}", targetHost, targetPort);
                    handleProxyConnect(ctx, targetHost, targetPort);
                }
                
            } catch (Exception e) {
                logger.error("处理HTTP CONNECT请求失败: {}", e.getMessage(), e);
                sendErrorResponse(ctx, HttpResponseStatus.BAD_REQUEST, 
                    "Invalid CONNECT request: " + e.getMessage());
            }
        }
        
        /**
         * 处理直连
         */
        private void handleDirectConnect(ChannelHandlerContext ctx, String targetHost, int targetPort) {
            // 使用DirectConnectionHandler单例创建直连
            DirectConnectionHandler directHandler = DirectConnectionHandler.getInstance();

            // 创建HTTP CONNECT直连会话处理器
            HttpDirectSessionHandler sessionHandler = new HttpDirectSessionHandler(ctx, targetHost, targetPort);

            int sessionId = directHandler.createDirectConnection(targetHost, targetPort, sessionHandler);

            if (sessionId > 0) {
                sessionHandler.setSessionId(sessionId);
                logger.debug("HTTP CONNECT直连会话创建: sessionId={}, target={}:{}",
                    sessionId, targetHost, targetPort);
            } else {
                logger.error("HTTP CONNECT直连会话创建失败: {}:{}", targetHost, targetPort);
                sendErrorResponse(ctx, HttpResponseStatus.BAD_GATEWAY,
                    "Failed to create direct connection");
            }
        }
        
        /**
         * 处理代理连接
         */
        private void handleProxyConnect(ChannelHandlerContext ctx, String targetHost, int targetPort) {
            // 创建会话处理器
            MultiplexSessionHandler sessionHandler = new MultiplexSessionHandler(
                ctx.channel(), targetHost, targetPort);

            // 通过ConnectionManager创建会话
            int tempSessionId = connectionManager.createSession(targetHost, targetPort, sessionHandler);

            if (tempSessionId != 0) {  // 0表示完全失败，负数是正常的临时sessionId
                sessionHandler.setSessionId(tempSessionId);
                logger.debug("HTTP CONNECT代理会话请求已发送: tempSessionId={}, target={}:{}",
                    tempSessionId, targetHost, targetPort);
            } else {
                logger.error("HTTP CONNECT代理会话创建失败: {}:{}", targetHost, targetPort);
                sendErrorResponse(ctx, HttpResponseStatus.BAD_GATEWAY,
                    "Failed to create proxy session");
            }
        }
        
        /**
         * 发送连接成功响应
         */
        private void sendConnectSuccessResponse(ChannelHandlerContext ctx) {
            FullHttpResponse response = new DefaultFullHttpResponse(
                HttpVersion.HTTP_1_1, HttpResponseStatus.OK);
            response.headers().set(HttpHeaderNames.CONNECTION, "keep-alive");
            ctx.writeAndFlush(response);
        }
        
        /**
         * 发送错误响应
         */
        private void sendErrorResponse(ChannelHandlerContext ctx, HttpResponseStatus status, String message) {
            FullHttpResponse response = new DefaultFullHttpResponse(
                HttpVersion.HTTP_1_1, status);
            response.headers().set(HttpHeaderNames.CONTENT_TYPE, "text/plain");
            response.headers().set(HttpHeaderNames.CONNECTION, "close");
            
            if (message != null) {
                response.content().writeBytes(message.getBytes());
                response.headers().set(HttpHeaderNames.CONTENT_LENGTH, message.length());
            }
            
            ctx.writeAndFlush(response).addListener(ChannelFutureListener.CLOSE);
        }
        
        /**
         * 解析主机和端口
         */
        private String[] parseHostPort(String uri) {
            String[] parts = uri.split(":");
            if (parts.length != 2) {
                throw new IllegalArgumentException("Invalid host:port format: " + uri);
            }
            return parts;
        }
        
        @Override
        public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
            logger.error("HTTP代理处理异常: {}", cause.getMessage(), cause);
            ctx.close();
        }
    }

    /**
     * 发送错误响应
     */
    private void sendErrorResponse(ChannelHandlerContext ctx, HttpResponseStatus status, String message) {
        FullHttpResponse response = new DefaultFullHttpResponse(
            HttpVersion.HTTP_1_1, status);
        response.headers().set(HttpHeaderNames.CONTENT_TYPE, "text/plain; charset=UTF-8");
        response.headers().set(HttpHeaderNames.CONNECTION, HttpHeaderValues.CLOSE);

        if (message != null) {
            ByteBuf content = ctx.alloc().buffer();
            content.writeBytes(message.getBytes());
            response.content().writeBytes(content);
            content.release();
            response.headers().set(HttpHeaderNames.CONTENT_LENGTH, response.content().readableBytes());
        }

        ctx.writeAndFlush(response).addListener(ChannelFutureListener.CLOSE);
    }

    /**
     * HTTP CONNECT直连会话处理器
     */
    private class HttpDirectSessionHandler implements SessionHandler {
        private final ChannelHandlerContext ctx;
        private final String targetHost;
        private final int targetPort;
        private volatile int sessionId = -1;
        private volatile boolean connected = false;

        public HttpDirectSessionHandler(ChannelHandlerContext ctx, String targetHost, int targetPort) {
            this.ctx = ctx;
            this.targetHost = targetHost;
            this.targetPort = targetPort;
        }

        public void setSessionId(int sessionId) {
            this.sessionId = sessionId;
        }

        @Override
        public void onConnectResponse(boolean success, int assignedSessionId) {
            if (success) {
                this.sessionId = assignedSessionId;
                this.connected = true;

                logger.info("HTTP CONNECT直连建立成功: sessionId={}, target={}:{}",
                    sessionId, targetHost, targetPort);

                // 发送HTTP 200 Connection Established响应
                sendConnectSuccessResponse(ctx);

                // 移除HTTP处理器，切换到透明代理模式
                removeHttpHandlers(ctx);

                // 添加数据转发处理器
                ctx.pipeline().addLast("direct-forwarder", new DirectDataForwarder(sessionId));

            } else {
                logger.error("HTTP CONNECT直连建立失败: target={}:{}", targetHost, targetPort);
                HttpInbound.this.sendErrorResponse(ctx, HttpResponseStatus.BAD_GATEWAY,
                    "Failed to connect to target server");
            }
        }

        @Override
        public void onData(byte[] data) {
            if (ctx.channel().isActive()) {
                ByteBuf buffer = ctx.alloc().buffer(data.length);
                buffer.writeBytes(data);
                ctx.writeAndFlush(buffer);
            }
        }

        @Override
        public void onClose() {
            logger.debug("HTTP CONNECT直连会话关闭: sessionId={}, target={}:{}",
                sessionId, targetHost, targetPort);

            if (ctx.channel().isActive()) {
                ctx.close();
            }
        }

        @Override
        public void close() {
            logger.debug("关闭HTTP CONNECT直连会话处理器: sessionId={}, target={}:{}",
                sessionId, targetHost, targetPort);

            if (ctx.channel().isActive()) {
                ctx.close();
            }
        }
    }

    /**
     * 直连数据转发处理器
     */
    private class DirectDataForwarder extends ChannelInboundHandlerAdapter {
        private final int sessionId;

        public DirectDataForwarder(int sessionId) {
            this.sessionId = sessionId;
        }

        @Override
        public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
            if (msg instanceof ByteBuf) {
                ByteBuf buffer = (ByteBuf) msg;
                if (buffer.readableBytes() > 0) {
                    // Retain the buffer to increment the reference count since we're passing it to another thread
                    buffer.retain();
                    // Send data directly without copying to byte array first
                    DirectConnectionHandler.getInstance().sendData(sessionId, buffer);
                } else {
                    // Release the buffer if we're not using it
                    buffer.release();
                }
            }
        }

        @Override
        public void channelInactive(ChannelHandlerContext ctx) throws Exception {
            logger.debug("HTTP客户端连接关闭，关闭直连会话: sessionId={}", sessionId);
            DirectConnectionHandler.getInstance().closeConnection(sessionId);
            super.channelInactive(ctx);
        }

        @Override
        public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
            logger.error("HTTP CONNECT直连数据转发异常: sessionId={}, 原因: {}",
                sessionId, cause.getMessage(), cause);
            ctx.close();
        }
    }

    /**
     * 发送连接成功响应
     */
    private void sendConnectSuccessResponse(ChannelHandlerContext ctx) {
        FullHttpResponse response = new DefaultFullHttpResponse(
            HttpVersion.HTTP_1_1, HttpResponseStatus.OK);
        response.headers().set(HttpHeaderNames.CONNECTION, "keep-alive");
        ctx.writeAndFlush(response);
        logger.debug("发送HTTP 200 Connection Established响应");
    }

    /**
     * 移除HTTP处理器
     */
    private void removeHttpHandlers(ChannelHandlerContext ctx) {
        try {
            if (ctx.pipeline().get("http-codec") != null) {
                ctx.pipeline().remove("http-codec");
            }
            if (ctx.pipeline().get("http-aggregator") != null) {
                ctx.pipeline().remove("http-aggregator");
            }
            if (ctx.pipeline().get("http-handler") != null) {
                ctx.pipeline().remove("http-handler");
            }
            logger.debug("已移除HTTP处理器，切换到透明代理模式");
        } catch (Exception e) {
            logger.warn("移除HTTP处理器时发生异常: {}", e.getMessage());
        }
    }
}
