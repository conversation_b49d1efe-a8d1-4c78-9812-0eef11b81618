@echo off
echo Building proxy-client native executable...

REM Check if we're in Visual Studio Developer Command Prompt
if not defined VCINSTALLDIR (
    echo Warning: Visual Studio environment not detected.
    echo For best results, run this script from "x64 Native Tools Command Prompt for VS 2022"
    echo.
    echo If you don't have Visual Studio installed:
    echo 1. Download Visual Studio 2022 Community from: https://visualstudio.microsoft.com/downloads/
    echo 2. Install with "Desktop development with C++" workload
    echo 3. Or install only Build Tools for Visual Studio 2022
    echo.
    echo Continuing anyway...
    echo.
)

REM Check if GraalVM is installed
where native-image >nul 2>nul
if %errorlevel% neq 0 (
    echo Error: native-image command not found. Please install GraalVM and add it to PATH.
    echo Download GraalVM from: https://www.graalvm.org/downloads/
    echo After installation, run: gu install native-image
    exit /b 1
)

REM Clean and compile the project
echo Cleaning and compiling project...
call mvn clean compile

if %errorlevel% neq 0 (
    echo Error: Maven compilation failed
    exit /b 1
)

REM Build native image using Maven plugin
echo Building native image...
call mvn -Pnative native:compile

if %errorlevel% neq 0 (
    echo Error: Native image build failed
    exit /b 1
)

echo Native executable built successfully!
echo Location: target\proxy-client.exe
echo.
echo Usage: target\proxy-client.exe [local_port] [proxy_server_host] [proxy_server_port]
echo Example: target\proxy-client.exe 1080 localhost 8888
