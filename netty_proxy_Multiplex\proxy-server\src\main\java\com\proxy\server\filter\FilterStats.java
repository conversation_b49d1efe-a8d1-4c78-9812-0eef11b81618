package com.proxy.server.filter;

/**
 * 过滤统计信息类
 */
public class FilterStats {
    private final long totalRequests;
    private final long blockedByGeoLocation;
    private final long blockedByDomainFilter;
    private final long allowedRequests;
    
    public FilterStats(long totalRequests, long blockedByGeoLocation, 
                      long blockedByDomainFilter, long allowedRequests) {
        this.totalRequests = totalRequests;
        this.blockedByGeoLocation = blockedByGeoLocation;
        this.blockedByDomainFilter = blockedByDomainFilter;
        this.allowedRequests = allowedRequests;
    }
    
    public long getTotalRequests() {
        return totalRequests;
    }
    
    public long getBlockedByGeoLocation() {
        return blockedByGeoLocation;
    }
    
    public long getBlockedByDomainFilter() {
        return blockedByDomainFilter;
    }
    
    public long getAllowedRequests() {
        return allowedRequests;
    }
    
    public long getTotalBlocked() {
        return blockedByGeoLocation + blockedByDomainFilter;
    }
    
    public double getBlockRate() {
        return totalRequests > 0 ? (double) getTotalBlocked() / totalRequests : 0.0;
    }
    
    public double getAllowRate() {
        return totalRequests > 0 ? (double) allowedRequests / totalRequests : 0.0;
    }
    
    @Override
    public String toString() {
        return String.format(
            "FilterStats{总请求: %d, 地理位置阻止: %d, 域名过滤阻止: %d, 允许: %d, 阻止率: %.2f%%}",
            totalRequests, blockedByGeoLocation, blockedByDomainFilter, 
            allowedRequests, getBlockRate() * 100
        );
    }
}
