# 📝 更新日志

## [v2.1.0] - 2025-07-20

### 🚀 重大功能更新

#### 🌍 地理位置过滤系统
- **新增**: 完整的地理位置过滤配置支持
- **新增**: 可配置的DNS缓存和IP地理位置缓存
- **新增**: 智能缓存管理，支持超时时间和大小限制配置
- **新增**: 自动更新IP段数据，支持配置更新间隔
- **优化**: 地理位置过滤性能，增加多级缓存机制

#### 🛡️ 恶意内容过滤系统
- **新增**: 恶意域名黑名单自动在线更新
- **新增**: 恶意关键词检测和在线更新
- **新增**: 160+个精心筛选的合法海外网站白名单
- **新增**: 多数据源支持，确保数据更新的可靠性
- **新增**: 智能关键词提取和验证机制

#### 📁 外部配置管理
- **新增**: `config.ext.dir` 统一配置目录管理
- **新增**: 配置文件自动发现机制
- **新增**: 配置文件优先级加载策略
- **新增**: 启动时自动初始化所有配置文件和组件
- **优化**: 配置加载容错机制，支持降级到默认配置

#### 📡 在线数据更新
- **新增**: 恶意域名数据自动从多个威胁情报源更新
- **新增**: 恶意关键词数据在线更新和智能提取
- **新增**: 中国IP段数据定期自动更新
- **新增**: 异步更新机制，不阻塞系统启动
- **新增**: 更新后自动保存到配置目录
- **新增**: 在线数据源URL完全可配置，支持自定义数据源
- **优化**: 配置加载失败时自动降级到内置默认数据源

### 🔧 配置增强

#### 地理位置过滤配置
```yaml
geo-location-filter:
  enable: true                      # 启用地理位置过滤
  block-overseas-suspicious: true   # 阻止海外可疑网站
  enable-domain-filter: true        # 启用域名过滤
  enable-keyword-filter: true       # 启用关键词过滤
  enable-whitelist: true            # 启用白名单
  dns-cache-timeout-minutes: 10     # DNS缓存超时时间
  ip-cache-timeout-minutes: 120     # IP缓存超时时间
  max-cache-size: 50000            # 最大缓存条目数
  auto-update-ip-ranges: true       # 自动更新IP段数据
  update-interval-hours: 24         # 更新间隔(小时)

  # 在线数据源配置
  online-data-sources:
    # 恶意域名数据源
    malicious-domains:
      - "https://raw.githubusercontent.com/StevenBlack/hosts/master/hosts"
      - "https://someonewhocares.org/hosts/zero/hosts"
      - "https://raw.githubusercontent.com/AdguardTeam/AdguardFilters/master/BaseFilter/sections/adservers.txt"

    # 恶意关键词数据源
    malicious-keywords:
      - "https://raw.githubusercontent.com/crazy-max/WindowsSpyBlocker/master/data/hosts/spy.txt"

    # 中国IP段数据源
    china-ip-ranges:
      - "https://raw.githubusercontent.com/mayaxcn/china-ip-list/master/chnroute.txt"
      - "https://raw.githubusercontent.com/17mon/china_ip_list/master/china_ip_list.txt"
```

#### 客户端在线数据源配置
```yaml
online-data-sources:
  # 中国IP段数据源
  china-ip-ranges:
    - "https://raw.githubusercontent.com/mayaxcn/china-ip-list/master/chnroute.txt"
    - "https://raw.githubusercontent.com/17mon/china_ip_list/master/china_ip_list.txt"
```

#### 配置目录结构
```
configs/development/
├── sever/                          # 服务器配置目录
│   ├── proxy-server.yml            # 主配置文件
│   ├── china-ip-ranges.txt         # 中国IP段数据
│   ├── malicious-domains.txt       # 恶意域名黑名单
│   ├── malicious-keywords.txt      # 恶意关键词列表
│   └── whitelist-domains.txt       # 白名单域名
└── client/                         # 客户端配置目录
    ├── proxy-client.yml            # 主配置文件
    └── china-ip-ranges.txt         # 中国IP段数据
```

### 📊 数据文件更新

#### 白名单域名 (160个)
- **搜索引擎**: Google、Bing、DuckDuckGo等主流搜索引擎
- **教育机构**: MIT、Stanford、Harvard、Oxford等世界顶级大学
- **技术开发**: GitHub、云服务、开发工具平台
- **办公生产力**: 微软、Google办公套件
- **新闻媒体**: BBC、CNN、Reuters等权威媒体
- **科技媒体**: TechCrunch、Wired、The Verge等科技资讯

#### 恶意内容数据源
- **恶意域名**: 支持从多个在线威胁情报源自动更新
- **恶意关键词**: 智能从恶意域名中提取可疑关键词
- **中国IP段**: 基于APNIC官方数据，支持定期自动更新

### 🚀 性能优化

#### 缓存系统
- **DNS缓存**: 可配置超时时间，默认10分钟
- **IP地理位置缓存**: 可配置超时时间，默认120分钟
- **缓存大小限制**: 可配置最大缓存条目数，默认50000
- **智能清理**: 自动清理过期缓存和最旧条目

#### 启动优化
- **异步初始化**: 耗时操作异步执行，不阻塞启动
- **并发安全**: 所有缓存和数据结构线程安全
- **容错机制**: 配置加载失败时自动降级到默认配置

### 🔄 兼容性

#### 向后兼容
- **配置文件**: 保持对旧版配置文件的兼容性
- **API接口**: 所有现有API接口保持不变
- **命令行参数**: 支持所有现有的命令行参数

#### 配置迁移
- **自动发现**: 支持多种配置文件命名格式
- **优先级加载**: 新配置优先，旧配置作为备选
- **平滑升级**: 无需修改现有配置即可使用新功能

### 📚 文档更新

#### 新增文档
- **FEATURES.md**: 功能特性详解
- **CHANGELOG.md**: 更新日志

#### 更新文档
- **README.md**: 更新核心特性和配置概述
- **CONFIGURATION_GUIDE.md**: 新增地理位置过滤和恶意内容过滤配置说明

### 🐛 问题修复

#### 配置管理
- **修复**: 配置文件路径解析问题
- **修复**: 配置加载失败时的异常处理
- **优化**: 配置验证和错误提示

#### 缓存管理
- **修复**: 缓存清理时的并发安全问题
- **优化**: 缓存命中率和内存使用效率
- **增强**: 缓存统计和监控功能

### 🔮 下一版本预告

#### 计划功能
- **Web管理界面**: 基于Web的配置管理和监控界面
- **API接口**: RESTful API支持动态配置更新
- **集群支持**: 多节点集群部署和负载均衡
- **更多数据源**: 支持更多威胁情报数据源

---

## [v2.0.x] - 历史版本

### 主要功能
- 多路复用代理协议
- SSL/TLS加密支持
- Basic认证
- 多组件架构
- GraalVM Native Image支持
- 地址过滤功能
- 性能监控系统

---

**完整更新历史**: 查看 [GitHub Releases](https://github.com/your-repo/releases) 获取详细的版本历史信息。
