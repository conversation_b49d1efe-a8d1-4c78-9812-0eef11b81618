<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="proxy-server" />
        <module name="proxy-client" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="cursor-comment-scraper" target="17" />
    </bytecodeTargetLevel>
  </component>
</project>