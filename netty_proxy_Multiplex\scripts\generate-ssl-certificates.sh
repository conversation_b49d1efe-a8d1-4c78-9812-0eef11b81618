#!/bin/bash
# SSL证书生成脚本 - Linux/Mac版本
# 用于生成开发测试环境的自签名证书

echo "========================================"
echo "SSL证书生成脚本"
echo "========================================"

SERVER_KEYSTORE="server.p12"
CLIENT_KEYSTORE="client.p12"
TRUSTSTORE="truststore.p12"
PASSWORD="xiang1"
VALIDITY=365

echo
echo "正在生成服务器证书..."
keytool -genkeypair \
    -alias server \
    -keyalg RSA \
    -keysize 2048 \
    -storetype PKCS12 \
    -keystore $SERVER_KEYSTORE \
    -storepass $PASSWORD \
    -keypass $PASSWORD \
    -validity $VALIDITY \
    -dname "CN=localhost,OU=Development,O=ProxyServer,L=Beijing,ST=Beijing,C=CN" \
    -ext SAN=dns:localhost,ip:127.0.0.1

if [ $? -ne 0 ]; then
    echo "服务器证书生成失败！"
    exit 1
fi

echo "服务器证书生成成功: $SERVER_KEYSTORE"

echo
echo "正在生成客户端证书..."
keytool -genkeypair \
    -alias client \
    -keyalg RSA \
    -keysize 2048 \
    -storetype PKCS12 \
    -keystore $CLIENT_KEYSTORE \
    -storepass $PASSWORD \
    -keypass $PASSWORD \
    -validity $VALIDITY \
    -dname "CN=proxy-client,OU=Development,O=ProxyClient,L=Beijing,ST=Beijing,C=CN"

if [ $? -ne 0 ]; then
    echo "客户端证书生成失败！"
    exit 1
fi

echo "客户端证书生成成功: $CLIENT_KEYSTORE"

echo
echo "正在创建信任库..."

# 导出服务器证书
keytool -exportcert \
    -alias server \
    -keystore $SERVER_KEYSTORE \
    -storepass $PASSWORD \
    -file server.crt

# 导出客户端证书
keytool -exportcert \
    -alias client \
    -keystore $CLIENT_KEYSTORE \
    -storepass $PASSWORD \
    -file client.crt

# 创建信任库并导入服务器证书（客户端使用）
keytool -importcert \
    -alias server \
    -keystore $TRUSTSTORE \
    -storepass $PASSWORD \
    -file server.crt \
    -noprompt

# 导入客户端证书到信任库（服务器双向认证使用）
keytool -importcert \
    -alias client \
    -keystore $TRUSTSTORE \
    -storepass $PASSWORD \
    -file client.crt \
    -noprompt

echo "信任库创建成功: $TRUSTSTORE"

echo
echo "正在复制证书到项目目录..."

# 复制服务器证书到proxy-server
cp $SERVER_KEYSTORE ../proxy-server/src/main/resources/
cp $TRUSTSTORE ../proxy-server/src/main/resources/

# 复制客户端证书到proxy-client
cp $CLIENT_KEYSTORE ../proxy-client/src/main/resources/
cp $TRUSTSTORE ../proxy-client/src/main/resources/

echo
echo "清理临时文件..."
rm server.crt
rm client.crt

echo
echo "========================================"
echo "证书生成完成！"
echo "========================================"
echo "服务器证书: $SERVER_KEYSTORE"
echo "客户端证书: $CLIENT_KEYSTORE"
echo "信任库: $TRUSTSTORE"
echo "密码: $PASSWORD"
echo "有效期: $VALIDITY 天"
echo
echo "证书已复制到相应的项目资源目录"
echo "========================================"
